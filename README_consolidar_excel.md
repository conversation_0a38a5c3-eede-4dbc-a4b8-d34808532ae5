# Consolidación de Archivos Excel

Este proyecto proporciona herramientas para consolidar múltiples archivos Excel en uno solo, manteniendo la estructura de los datos.

## Descripción

El script principal `consolidar_excel.py` permite abrir múltiples archivos Excel (hasta 48 o más), concatenar su información y guardar el resultado en un archivo llamado "consolidado.xlsx". Para los archivos después del primero, se omiten los encabezados (primera fila) ya que todos los archivos tienen la misma estructura.

## Requisitos

- Python 3.6 o superior
- Bibliotecas:
  - polars
  - pandas
  - numpy (solo para pruebas)

## Archivos incluidos

1. **consolidar_excel.py**: Script principal para consolidar archivos Excel
2. **test_consolidar.py**: Script de prueba para verificar la funcionalidad
3. **pivot_lecturas.py**: Script existente que ha sido modificado para incluir funcionalidad de consolidación

## Uso

### Usando consolidar_excel.py (Recomendado)

Este script está diseñado específicamente para la tarea de consolidación de archivos Excel.

```bash
# Procesar todos los archivos Excel en un directorio
python consolidar_excel.py C:\Users\<USER>\OneDrive - Grupo CGE\2025\04_InformeClientes\Nuevos Consumos

# Procesar archivos que coinciden con un patrón
python consolidar_excel.py "C:\Users\<USER>\OneDrive - Grupo CGE\2025\04_InformeClientes\Nuevos Consumos\*.xlsx"

# Uso interactivo (solicitará la ruta del directorio)
python consolidar_excel.py
```

### Usando pivot_lecturas.py

Este script existente ha sido modificado para incluir la funcionalidad de consolidación.

```bash
# Procesar todos los archivos Excel en un directorio
python pivot_lecturas.py -m C:\ruta\al\directorio

# Procesar archivos que coinciden con un patrón
python pivot_lecturas.py -m "C:\ruta\al\directorio\*.xlsx"

# Ver ayuda
python pivot_lecturas.py -h
```

### Pruebas

Para verificar que la funcionalidad de consolidación funciona correctamente:

```bash
python test_consolidar.py
```

Este script creará archivos de prueba, los consolidará y verificará que el resultado sea correcto.

## Funcionalidades

- **Lectura de múltiples archivos**: Puede procesar hasta 48 archivos Excel o más.
- **Omisión de encabezados**: Para los archivos después del primero, se omiten los encabezados.
- **Manejo de errores**: Detecta y reporta errores en archivos individuales sin detener el proceso completo.
- **Guardado automático**: Guarda el resultado en "consolidado.xlsx" por defecto.
- **Compatibilidad**: Funciona con archivos .xlsx y .xls.

## Ejemplo de flujo de trabajo

1. Coloque todos los archivos Excel que desea consolidar en un directorio.
2. Ejecute `python consolidar_excel.py` y proporcione la ruta del directorio.
3. El script procesará todos los archivos y generará "consolidado.xlsx" con todos los datos.

## Notas técnicas

- El script utiliza la biblioteca Polars para un procesamiento eficiente de los datos.
- Para la exportación a Excel, se convierte temporalmente a Pandas.
- Si ocurre un error al guardar como Excel, intentará guardar como CSV como alternativa.