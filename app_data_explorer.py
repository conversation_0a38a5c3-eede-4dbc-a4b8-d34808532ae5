from fastapi import FastAP<PERSON>, HTTPException, Query, BackgroundTasks
from fastapi.responses import FileResponse, HTMLResponse
from pydantic import BaseModel
from pathlib import Path
from typing import Optional, List, Dict, Any
import polars as pl
import pandas as pd
from datetime import datetime, date
import tempfile
import os
from enum import Enum

# Configuración
ALL_DATA_FOLDER = Path(r"C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\CGE_Automatizacion\All_Data")
TEMP_FOLDER = Path(tempfile.gettempdir()) / "data_explorer_exports"
TEMP_FOLDER.mkdir(exist_ok=True)

app = FastAPI(
    title="Data Explorer API",
    description="API para explorar y filtrar datos de archivos CMg e IVT",
    version="1.0.0"
)

class FileType(str, Enum):
    CMG = "CMg"
    IVT = "IVT"

class FilterRequest(BaseModel):
    file_type: FileType
    year_start: int
    month_start: int
    year_end: int
    month_end: int
    filters: Dict[str, Any] = {}
    export_format: str = "excel"  # excel, csv, parquet
    output_filename: Optional[str] = None

class FileInfo(BaseModel):
    filename: str
    file_type: str
    year: int
    month: int
    exists: bool
    size_mb: Optional[float] = None

def crea_rango(year_i: str, month_i: str, year_f: str, month_f: str):
    """Genera rango de fechas en formato YYYY-MM"""
    year_i, month_i = int(year_i), int(month_i)
    year_f, month_f = int(year_f), int(month_f)
    
    current_year, current_month = year_i, month_i
    
    while (current_year < year_f) or (current_year == year_f and current_month <= month_f):
        yield f"{current_year}-{str(current_month).zfill(2)}"
        
        if current_month == 12:
            current_year += 1
            current_month = 1
        else:
            current_month += 1

def get_file_path(file_type: FileType, year: int, month: int) -> Path:
    """Construye la ruta del archivo según el tipo y fecha"""
    year_str = str(year)[-2:]  # Últimos 2 dígitos del año
    month_str = str(month).zfill(2)
    
    if file_type == FileType.CMG:
        filename = f"CMg_{year_str}_{month_str}_def.parquet"
    else:  # IVT
        filename = f"IVT_{year_str}_{month_str}.parquet"
    
    return ALL_DATA_FOLDER / filename

@app.get("/", response_class=HTMLResponse)
async def root():
    """Página principal con documentación básica"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Data Explorer API</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .endpoint { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
            .method { color: #fff; padding: 3px 8px; border-radius: 3px; font-size: 12px; }
            .get { background: #61affe; }
            .post { background: #49cc90; }
        </style>
    </head>
    <body>
        <h1>Data Explorer API</h1>
        <p>API para explorar y filtrar datos de archivos CMg e IVT</p>
        
        <h2>Endpoints Disponibles:</h2>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <strong>/files/available</strong> - Lista archivos disponibles
        </div>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <strong>/files/columns/{file_type}</strong> - Obtiene columnas de un tipo de archivo
        </div>
        
        <div class="endpoint">
            <span class="method post">POST</span>
            <strong>/data/filter</strong> - Filtra datos y exporta a Excel
        </div>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <strong>/data/preview</strong> - Vista previa de datos
        </div>
        
        <p><a href="/docs">📖 Documentación completa (Swagger UI)</a></p>
        <p><a href="/redoc">📋 Documentación alternativa (ReDoc)</a></p>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/files/available", response_model=List[FileInfo])
async def get_available_files(
    file_type: Optional[FileType] = None,
    year: Optional[int] = None
):
    """Lista todos los archivos disponibles en la carpeta All_Data"""
    files_info = []
    
    # Buscar archivos según el patrón
    if file_type:
        pattern = f"{file_type.value}*.parquet"
    else:
        pattern = "*.parquet"
    
    for file_path in ALL_DATA_FOLDER.glob(pattern):
        try:
            # Extraer información del nombre del archivo
            filename = file_path.name
            
            if filename.startswith("CMg_"):
                # Formato: CMg_YY_MM_def.parquet
                parts = filename.replace("CMg_", "").replace("_def.parquet", "").split("_")
                if len(parts) >= 2:
                    file_year = 2000 + int(parts[0])
                    file_month = int(parts[1])
                    file_type_str = "CMg"
                else:
                    continue
                    
            elif filename.startswith("IVT_"):
                # Formato: IVT_YY_MM.parquet
                parts = filename.replace("IVT_", "").replace(".parquet", "").split("_")
                if len(parts) >= 2:
                    file_year = 2000 + int(parts[0])
                    file_month = int(parts[1])
                    file_type_str = "IVT"
                else:
                    continue
            else:
                continue
            
            # Filtrar por año si se especifica
            if year and file_year != year:
                continue
            
            # Calcular tamaño del archivo
            size_mb = file_path.stat().st_size / (1024 * 1024)
            
            files_info.append(FileInfo(
                filename=filename,
                file_type=file_type_str,
                year=file_year,
                month=file_month,
                exists=True,
                size_mb=round(size_mb, 2)
            ))
            
        except (ValueError, IndexError):
            continue
    
    # Ordenar por año y mes
    files_info.sort(key=lambda x: (x.year, x.month))
    
    return files_info

@app.get("/files/columns/{file_type}")
async def get_file_columns(file_type: FileType):
    """Obtiene las columnas disponibles de un tipo de archivo"""
    # Buscar el archivo más reciente de este tipo
    pattern = f"{file_type.value}*.parquet"
    files = list(ALL_DATA_FOLDER.glob(pattern))
    
    if not files:
        raise HTTPException(status_code=404, detail=f"No se encontraron archivos de tipo {file_type.value}")
    
    # Tomar el archivo más reciente
    latest_file = max(files, key=lambda x: x.stat().st_mtime)
    
    try:
        # Leer solo el esquema del archivo
        df = pl.read_parquet(latest_file)
        columns_info = {
            "columns": df.columns,
            "dtypes": {col: str(dtype) for col, dtype in zip(df.columns, df.dtypes)},
            "sample_file": latest_file.name,
            "total_columns": len(df.columns),
            "total_rows": df.height
        }
        return columns_info
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al leer el archivo: {str(e)}")

@app.get("/data/preview")
async def preview_data(
    file_type: FileType,
    year: int = Query(..., description="Año (formato completo, ej: 2024)"),
    month: int = Query(..., ge=1, le=12, description="Mes (1-12)"),
    limit: int = Query(10, ge=1, le=1000, description="Número de filas a mostrar")
):
    """Obtiene una vista previa de los datos"""
    file_path = get_file_path(file_type, year, month)
    
    if not file_path.exists():
        raise HTTPException(status_code=404, detail=f"Archivo no encontrado: {file_path.name}")
    
    try:
        df = pl.read_parquet(file_path).head(limit)
        
        return {
            "file": file_path.name,
            "total_rows_in_file": pl.read_parquet(file_path).height,
            "preview_rows": limit,
            "columns": df.columns,
            "data": df.to_dicts()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al leer el archivo: {str(e)}")

def cleanup_temp_file(file_path: Path):
    """Limpia archivos temporales después de un tiempo"""
    try:
        if file_path.exists():
            os.remove(file_path)
    except:
        pass

@app.post("/data/filter")
async def filter_and_export_data(request: FilterRequest, background_tasks: BackgroundTasks):
    """Filtra datos según los criterios especificados y exporta a Excel"""
    
    # Validar fechas
    if request.year_start > request.year_end:
        raise HTTPException(status_code=400, detail="El año de inicio no puede ser mayor al año final")
    
    if request.year_start == request.year_end and request.month_start > request.month_end:
        raise HTTPException(status_code=400, detail="El mes de inicio no puede ser mayor al mes final")
    
    # Generar rango de fechas
    date_range = list(crea_rango(
        str(request.year_start), 
        str(request.month_start),
        str(request.year_end), 
        str(request.month_end)
    ))
    
    # Cargar y combinar datos
    combined_data = pl.DataFrame()
    processed_files = []
    
    for date_str in date_range:
        year, month = date_str.split('-')
        file_path = get_file_path(request.file_type, int(year), int(month))
        
        if file_path.exists():
            try:
                df = pl.read_parquet(file_path)
                
                # Aplicar filtros
                for column, filter_value in request.filters.items():
                    if column in df.columns:
                        if isinstance(filter_value, str):
                            # Filtro de texto (contiene)
                            df = df.filter(pl.col(column).str.to_uppercase().str.contains(filter_value.upper()))
                        elif isinstance(filter_value, list):
                            # Filtro de lista (está en)
                            df = df.filter(pl.col(column).is_in(filter_value))
                        else:
                            # Filtro de igualdad
                            df = df.filter(pl.col(column) == filter_value)
                
                combined_data = combined_data.vstack(df)
                processed_files.append(file_path.name)
                
            except Exception as e:
                print(f"Error procesando {file_path.name}: {str(e)}")
                continue
    
    if combined_data.height == 0:
        raise HTTPException(status_code=404, detail="No se encontraron datos que coincidan con los filtros")
    
    # Generar nombre de archivo
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if request.output_filename:
        base_name = request.output_filename
    else:
        base_name = f"{request.file_type.value}_filtered_{timestamp}"
    
    # Exportar según el formato solicitado
    if request.export_format.lower() == "excel":
        output_file = TEMP_FOLDER / f"{base_name}.xlsx"
        combined_data.to_pandas().to_excel(output_file, index=False, engine='openpyxl')
        media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        
    elif request.export_format.lower() == "csv":
        output_file = TEMP_FOLDER / f"{base_name}.csv"
        combined_data.write_csv(output_file)
        media_type = "text/csv"
        
    elif request.export_format.lower() == "parquet":
        output_file = TEMP_FOLDER / f"{base_name}.parquet"
        combined_data.write_parquet(output_file)
        media_type = "application/octet-stream"
        
    else:
        raise HTTPException(status_code=400, detail="Formato no soportado. Use: excel, csv, o parquet")
    
    # Programar limpieza del archivo temporal (después de 1 hora)
    background_tasks.add_task(cleanup_temp_file, output_file)
    
    # Información del resultado
    result_info = {
        "message": "Datos filtrados y exportados exitosamente",
        "total_rows": combined_data.height,
        "total_columns": len(combined_data.columns),
        "processed_files": processed_files,
        "date_range": f"{request.year_start}-{str(request.month_start).zfill(2)} a {request.year_end}-{str(request.month_end).zfill(2)}",
        "filters_applied": request.filters,
        "export_format": request.export_format,
        "download_url": f"/download/{output_file.name}"
    }
    
    return result_info

@app.get("/download/{filename}")
async def download_file(filename: str):
    """Descarga un archivo exportado"""
    file_path = TEMP_FOLDER / filename
    
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="Archivo no encontrado o expirado")
    
    # Determinar el tipo de contenido
    if filename.endswith('.xlsx'):
        media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    elif filename.endswith('.csv'):
        media_type = "text/csv"
    elif filename.endswith('.parquet'):
        media_type = "application/octet-stream"
    else:
        media_type = "application/octet-stream"
    
    return FileResponse(
        path=file_path,
        media_type=media_type,
        filename=filename
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
