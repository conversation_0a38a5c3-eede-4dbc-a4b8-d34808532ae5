import polars as pl
import pandas as pd

import urllib.request
import urllib.error
import tqdm
from pathlib import Path
import zipfile


class Descarga_CMg_Cen:
    def __init__(self, folder: Path):
        self.folder = folder
        self.url_cen = 'https://www.coordinador.cl/wp-content/uploads/'

        self.folder.mkdir(parents=True, exist_ok=True)

    def download_with_progress(self, url, file):
        req = urllib.request.Request(url, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept-Encoding': 'gzip, deflate'})
        try:
            response = urllib.request.urlopen(req)
        except urllib.error.HTTPError as e:
            print(f'Error en la descarga de: {url}')
            print(e)
            return

        total_size = int(response.info()['Content-Length'])
        block_size = 2 * 1024 * 1024

        progress_bar = tqdm.tqdm(total=total_size, unit='B', unit_scale=True)
        full_file = self.folder / file
        with open(full_file, 'wb') as file:
            while True:
                data = response.read(block_size)
                if not data:
                    break
                progress_bar.update(len(data))
                file.write(data)
            print('Archivo Descargado')
        progress_bar.close()

    def descarga_cmg_15min(self,  proceso: str, dia_inicial: int,
                           dia_final: int, mes: str, mes_publicacion: str,
                           agno: str, agno_publicacion: str):
        fallidos = []
        for dia in range(dia_inicial, dia_final):
            cmg = f'Antecedentes_CMG_Real_{proceso}_{agno}{mes}{str(dia).zfill(2)}.zip'
            cen = self.url_cen + fr'20{agno_publicacion}/{mes_publicacion}/'
            url = cen + cmg
            print(f'descargando: {url}', end=' ')
            try:
                self.download_with_progress(url, cmg)
            except Exception as e:
                fallidos.append(url)
                print(f'Error en la descarga de: {url}')
                print(e)

        return print(f'\n\nDescargas Fallidas: {fallidos}')

    def descomprime_zip(self, elimina_zip: bool = True) -> bool:
        zip_files = list(self.folder.glob('*.zip'))

        for i, file in enumerate(zip_files):
            nom_fecha = str(zip_files[i]).split('_')[-1].split('.')[0]
            nom_csv = f'CmgBarrasComparativo_20{nom_fecha}_20{nom_fecha}_15.csv'
            print(f'Descomprimiendo: {i + 1} de {len(zip_files)} - {nom_csv}',
                  end='\r')
            try:
                self.get_file_from_zip(file, nom_csv, delete_zip=elimina_zip)
            except Exception as e:
                print(e)
                return False
        return True

    def procesa_csv_cmg15min(self, final_file: str, file_usd: str,
                             elimina_csv: bool = True):
        csv_files = list(self.folder.glob("*.csv"))

        df_fin = pd.DataFrame()
        for i, file in enumerate(csv_files):
            print(f'Procesando: {i + 1} de {len(csv_files)} - {file.name}',
                  end='\r')
            df = pd.read_csv(file, sep=';', encoding='latin', decimal='.')
            if df.iloc[:, 6].sum() == 0:
                df = df.drop(df.columns[[6, 7, -1]], axis=1)
            else:
                df = df.drop(df.columns[[7, 8, -1]], axis=1)
            df_fin = pd.concat([df_fin, df])

        if elimina_csv:
            for file in csv_files:
                file.unlink()

        print('\nHomologando las columnas...')
        cmg_cols = df_fin.columns[df_fin.columns.str.contains('CM')]
        df_fin[cmg_cols] = df_fin[cmg_cols].bfill(axis=1)
        df_fin['CMg [USD/MWh]'] = df_fin[cmg_cols].iloc[:, 0]
        df_fin = df_fin.drop(cmg_cols, axis=1)

        df_fin['FECHA'] = pd.to_datetime(df_fin['FECHA'], format='%Y%m%d')
        df_fin['FECHA'] = (df_fin['FECHA'] + pd.to_timedelta(df_fin['HORA'],
                                                             unit='h') + pd.to_timedelta(
            df_fin['MINUTO'], unit='m'))
        df_fin = df_fin.drop(['INFO_BARRA_ID'], axis=1)
        df_fin = df_fin.drop(['INFO_BARRA_NOMBRE'], axis=1)
        df_fin = df_fin.drop(['MINUTO'], axis=1)
        df_fin = df_fin.drop(['HORA'], axis=1)

        print('Generando CMg_parquet y archivo excel de dólar...')
        df_fin.rename(columns={'FECHA': 'Fecha', 'BARRA': 'Barra'},
                      inplace=True)
        df_fin.to_parquet(self.folder / final_file)
        cols = ['Fecha', 'USD']
        barra = 'C.NAVIA_______110'
        df_usd = df_fin.loc[df_fin['Barra'] == barra][cols]
        df_usd.to_excel(self.folder / file_usd, index=False)

        print('Fin del proceso...')
        return df_fin, df_usd

    def get_file_from_zip(self, zip_full_path, nombre, delete_zip=False):
        zip_full_path = Path(zip_full_path)
        where = str(zip_full_path.parent)

        try:
            with zipfile.ZipFile(str(zip_full_path), 'r') as azip:
                for nombre_carpeta in azip.namelist():
                    if nombre in nombre_carpeta:
                        azip.extract(nombre_carpeta, path=where)
                        break
            ruta_extraido = zip_full_path.parent / nombre_carpeta
            ruta_renombrado = Path(where) / nombre

            ruta_extraido.rename(ruta_renombrado)
            if '03-Base' in str(zip_full_path):
                remove = glob.glob(str(self.folder / '03 Base*'))[0]
                shutil.rmtree(remove)

            if delete_zip:
                zip_full_path.unlink()
            exito = True
        except Exception as f_error:

            print('\nAlgo ocurrió: ', f_error)
            exito = False
        return exito


#%%
if __name__ == '__main__':
    #%%
    folder = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\General CGE Cx\Movimientos Mensuales\_Margen\2025\03_Mar\Facturación Marzo')
    marzo = Descarga_CMg_Cen(folder)
    #%%
    marzo.descarga_cmg_15min(
        proceso= 'pre',
        dia_inicial=25,
        dia_final=32,
        mes='03',
        mes_publicacion='03',
        agno='25',
        agno_publicacion='25')
    #%%
    marzo.descomprime_zip(False)
    #%%
    marzo.procesa_csv_cmg15min('CMg.parquet', 'dolar.xlsx')
    #%%
    cmg = pl.read_parquet(folder / 'CMg.parquet')
    #%%
    cmg['CMg [USD/MWh]'].mean()
    #%%
    dcli = pd.read_excel(
        r'C:\Users\<USER>\OneDrive - Grupo CGE\General CGE Cx\Facturación '
        r'PPAs\Cálculo Facturación\2025\202503\02. Consumos\Data_cli.xlsx')
    #%%
    barras = dcli.BARRA.unique().tolist()
    #%%
    cgec =cmg.filter(
        pl.col('Barra').str.to_uppercase().str.contains('|'.join(barras))
    )
    #%%
    df = cgec.drop(['__index_level_0__']).to_pandas()
    #%%
    consumos = pd.read_excel(
        r'C:\Users\<USER>\OneDrive - Grupo CGE\General CGE Cx\Movimientos '
        r'Mensuales\_Margen\2025\03_Mar\Facturación Marzo\CGE '
        r'COMERCIALIZADORA - 202503 - PRO.xlsx',
        sheet_name='DATA', usecols='A:I'
    )
    #consumos = consumos.loc[consumos['fecha_hora'] < '2025-03-21 00:00:00']
    consumos = consumos.merge(dcli, how='left', on='id_medidor')
    consumos.rename(columns={'BARRA': 'Barra'}, inplace=True)
    #%%
    consumos.rename(columns={'fecha_hora': 'Fecha'}, inplace=True)
    #%%
    consumos['Fecha'] = pd.to_datetime(consumos['Fecha'])
    df['Fecha'] = pd.to_datetime(df['Fecha'])
    #%%
    df2 = pd.merge(consumos, df, how='left', on=['Fecha', 'Barra'])
    #%%
    df2.to_excel(folder / 'consumos.xlsx', index=False)
    #%%
    med = pl.read_csv(
        folder / 'medidas feb25 total.csv',
        separator=';',
        ignore_errors=True
    )
    # Limpiamos los espacios y convertimos a float
    med = med.with_columns([
        pl.col(['Energia Activa Directa(15)', 'Energia Reactiva Directa(15)'])
        .str.strip_chars()  # elimina espacios en blanco
        .str.replace(',', '.')  # reemplaza comas por puntos
        .cast(pl.Float64)
    ])
    
    med = med.drop(['equipo', 'serie', 'denominacion', 'Energia Reactiva '
                                                      'Directa(15)'])

    med  = med.with_columns([
        pl.col('fecha Hora').str.strptime(pl.Datetime, '%d-%m-%Y %H:%M')
    ])
    #%%
    id = pd.read_excel(r'C:\Users\<USER>\OneDrive - Grupo CGE\General CGE '
                       r'Cx\Facturación PPAs\Cálculo Facturación\2025\202503\01. Envío Anto\Formato carga precios  CGE Cx 2025-02_small_entrega.xlsx')
    #%%
    med = med.to_pandas()
    med.rename(columns={'instalacion': 'INSTAL Cx'}, inplace=True)
    med = med.merge(id, how='left', on='INSTAL Cx')
    #%%
    med.to_excel(folder / 'medidas.xlsx', index=False)
    #%%
    med['fecha Hora'] = med['fecha Hora'] + pd.DateOffset(months=1)
    #%%
    med.rename(columns={'fecha Hora': 'Fecha'}, inplace=True)
    med = pd.merge(med, df, how='left', on=['Fecha', 'Barra'])
    #%%
    med.to_excel(folder / 'medidas.xlsx', index=False)