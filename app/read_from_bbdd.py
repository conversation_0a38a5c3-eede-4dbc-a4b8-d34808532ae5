#%%
"""
Scripts para sacar información de los archivos de la carpeta BBDD
"""
import polars as pl
import pandas as pd
import numpy as np
import time
from pathlib import Path


class read_from_bbdd:
    folder = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\CGE_Automatizacion\All_Data')
    def __init__(self, agno_i, mes_i, agno_f, mes_f):
        self.agno_i = agno_i
        self.mes_i = mes_i
        self.agno_f = agno_f
        self.mes_f = mes_f


    def get_cmg(self, barra: str):
        data = pl.DataFrame()
        rango_fechas = self.crea_rango(self.agno_i, self.mes_i, self.agno_f,
                                       self.mes_f)
        fecha = next(rango_fechas)
        while fecha is not None:
            print(f'Obteniendo datos de {fecha}')
            year = fecha.split('-')[0]
            month = fecha.split('-')[1].zfill(2)
            file, barra = self.busca_file(year, month, barra, 'CMg')
            df = self.get_data(file, 'Barra', barra)
            data = data.vstack(df)
            fecha = next(rango_fechas, None)
        return data

    def busca_barra_cmg(self, barra):
        file, barra= self.busca_file(self.agno_f, self.mes_f, barra, 'CMg')
        data = self.get_data(file, 'Barra', barra)
        data = data.select(pl.col('Barra').unique()).to_series()
        return data.sort()

    def get_ivt_cliente(self, cliente: str):
        """
        cliente: Cliente o clientes que se desea buscar
        """
        data = pl.DataFrame()
        rango_fechas = self.crea_rango(self.agno_i, self.mes_i, self.agno_f,
                                       self.mes_f)
        fecha = next(rango_fechas)
        while fecha is not None:
            print(f'Obteniendo datos de {fecha}')
            year = fecha.split('-')[0]
            month = fecha.split('-')[1].zfill(2)
            file, cliente = self.busca_file(year, month, cliente, 'IVT')
            df = self.get_data(file, 'Cliente', cliente)
            data = data.vstack(df)
            fecha = next(rango_fechas, None)
        data = data.rename({'nombre_barra': 'Barra'})
        return data

    def get_ivt_suministrador(self, suministrador: str):
        rango_fechas = self.crea_rango(self.agno_i, self.mes_i, self.agno_f,
                                       self.mes_f)
        data = pl.DataFrame()
        fecha = next(rango_fechas)
        while fecha is not None:
            print(f'Obteniendo datos de {fecha}')
            year = fecha.split('-')[0]
            month = fecha.split('-')[1]
            file, suministrador = self.busca_file(year, month, suministrador, 'IVT')
            df = self.get_data(file, 'propietario', suministrador)
            data = data.vstack(df)
            fecha = next(rango_fechas, None)
        data = data.rename({'nombre_barra': 'Barra'})
        return data

    def busca_suministrador_bdd(self, suministrador: str):
        file, suministrador = self.busca_file(self.agno_f, self.mes_f,
                                              suministrador, 'IVT')
        data = self.get_data(file, 'propietario', suministrador)
        data = data.select(pl.col(['propietario']).unique()).to_series()
        return data.sort()

    def get_data(self, file, col, filtro):
        df = pl.read_parquet(file)
        df = df.filter(pl.col(col).str.to_uppercase().str.contains(filtro))
        return df

    def busca_cliente_bdd(self, cliente: str):
        file, cliente = self.busca_file(self.agno_f, self.mes_f, cliente, 'IVT')
        data = self.get_data(file, 'Cliente', cliente)
        data = data.with_columns(
            [(pl.col('Cliente') + ', ' + pl.col('nombre_barra')).alias('cl')])
        data = data.select(pl.col(['cl']).unique()).to_series()
        return data.sort()

    def crea_rango(self, agno_i, mes_i, agno_f, mes_f):
        a_aux = self.agno_i
        mes_aux = self.mes_i
        while f'{a_aux}-{mes_aux}' != f'{self.agno_f}-{self.mes_f}':
            yield f'{a_aux}-{str(mes_aux).zfill(2)}'
            if int(mes_aux) == 12:
                a_aux = str(int(a_aux) + 1)
                mes_aux = 1

            else:
                a_aux = a_aux
                mes_aux = str(int(mes_aux) + 1)
        yield f'{agno_f}-{str(mes_f).zfill(2)}'

    def busca_file(self, agno, mes, search, opt):
        fin = '_def.parquet' if opt == 'CMg' else '.parquet'
        agno, mes = str(agno), str(mes)
        file_type = self.folder / f"{opt}_{agno[-2:]}_{mes.zfill(2)}{fin}"
        busqueda = '|'.join([x.strip().upper() for x in search.split(',')])
        return file_type, busqueda



#%%
if __name__ == '__main__':
    #%%
    agno = 2024
    bbdd = read_from_bbdd(agno, 1, agno, 12)

    #%% extrae data de CMg
    df = bbdd.get_cmg('polpaico, jahuel')
    print(df)
    #%%
    barras = bbdd.busca_barra_cmg('polpaico, jahuel')
    print(barras)
    #%% Extrae info cliente
    df2 = bbdd.get_ivt_cliente('PRODUCTOS FERNANDEZ')
    #%%
    print(df2)
    #%%
    df_p = df2.pivot(
        index = 'Fecha',
        columns = 'clave',
        values = 'Consumo [kWh]',
        aggregate_function = 'sum'
    )
    #%%
    folder_out = Path(
        r'C:\Users\<USER>\OneDrive - Grupo CGE\2025\Licitaciones\fout')
    df_p.to_pandas().to_excel(
        folder_out / 'consumos_productos_fernandez.xlsx',  index=False)
    #%%
    clientes = bbdd.busca_cliente_bdd('rosa, agricola')
    print(clientes)
    #%%
    list_comer = ('IMELSA_ENERGIA, EMOAC, ABASTIBLE, ATRIA_ENERGIA, '
                  'ACIERTA_ENERGIA, ENERGY_ASSET, LIPIGAS, Cinergia Chile, '
                  'TECNORED, NEOELECTRA_ENERGIA, FACTOR_LUZ, ENERQUINTA, ' 
                  'ECOM_GENERACION, CGE_C')

    suministradores = bbdd.busca_suministrador_bdd(list_comer)
    print(suministradores)
    #%%
    #sumi = bbdd.get_ivt_suministrador(list_comer)
    sumi = bbdd.get_ivt_suministrador('CGE_C')
    print(sumi)
    #%%
    sumi = sumi.to_pandas()
    barras = sumi.Barra.unique().tolist()
    print(barras)

    #%%
    cmg = bbdd.get_cmg(', '.join(barras))
    #%%
    sumi['Prop_b'] = sumi['Cliente'] + '_' + sumi['Barra'] + '_' + sumi['clave']
    #%%
    folder = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\General CGE Cx\CompraAGeneradores_(FCA)\Licitaciones\Licitación Guacolda')
    cmg_p = cmg_p.pivot_table(
        values='CMg [USD/MWh]',
        index=['Fecha', 'USD'],
        columns='Barra'
    )
    sumi_p = sumi.pivot_table(
        values='Consumo [kWh]',
        index=['Fecha'],
        columns='Prop_b'
    )

    #sumi.to_parquet(folder / f'competencia_{agno}.parquet')
    #%%

    #%%
    grupo = [sumi.Fecha.dt.month, 'propietario', 'clave', 'Barra', 'Cliente']
    sumi_mes = sumi.groupby(grupo).agg({'Consumo [kWh]': 'sum'}).reset_index()

    sumi_mes.to_excel(folder / f'competencia_mes_{agno}.xlsx', index=False)
    #%%
    sumi.Tipo_Medida.unique()
    #%%
    cmg_p = cmg.to_pandas()
    #%%
    cmg_p.to_excel(folder / f'cmg_{agno}.xlsx')
    sumi_p.to_excel(folder / f'consumo_cgec_{agno}.xlsx')

