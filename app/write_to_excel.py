import xlsxwriter
import polars as pl


def write_xlsx_values(df, path, sheet_name="Hoja1"):
    wb = xlsxwriter.Workbook(path, {'strings_to_urls': False})
    ws = wb.add_worksheet(sheet_name)

    # Escribe encabezados
    ws.write_row(0, 0, df.columns)
    # Escribe datos desde un array NumPy (rápido)
    data = df.to_numpy()  # si df es Polars: df.to_numpy()
    for i, row in enumerate(data, start=1):
        ws.write_row(i, 0, row)

    wb.close()