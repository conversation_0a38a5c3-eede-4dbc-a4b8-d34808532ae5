from pathlib import Path
import zipfile
import pandas as pd
import shutil
import polars as pl
import patoolib

class ReadZipfile:
    def __init__(self, file_name):
        self.file_name = Path(file_name)

    def read_zip_file(self, file='', where='', delete_zip=False):
        try:
            with zipfile.ZipFile(self.file_name, 'r') as azip:
                for archivo in azip.namelist():
                    if file in archivo:
                        azip.extract(archivo, path=where)
                        archivo_extraido = where / archivo

                        if '/' in archivo:
                            archivo_extraido.rename(where / file)
                            rem = self.file_name.parent / archivo.split('/')[0]
                            shutil.rmtree(rem)
                        else:
                            ar_deco = archivo.encode('cp437').decode('cp850')
                            archivo_extraido.rename(where / ar_deco)
                        break

        except Exception as f_error:
            print(f'Algo ocurrió al extraer Zip: \n{f_error}')
        finally:
            zipfile.ZipFile(self.file_name, 'r').close()
        if delete_zip:
            self.file_name.unlink()

    def list_file(self):
        s = zipfile.ZipFile(self.file_name).namelist()
        return s

class LeeMedidas:
    def __init__(self, anio, mes, carpeta, QMin=True):
        self.anio = anio
        self.mes = mes
        self.carpeta = Path(carpeta)
        self.QMin = QMin
        self.pth_out = self.carpeta / 'Procesados'
        self.pth_out.mkdir(exist_ok=True)
        self.ar_zip = f'Balance_Valorizado_{anio}{mes}_Data_VALORIZADO_15min.zip'
        self.ar_csv = f'Balance_Valorizado_{anio}{mes}_Data_VALORIZADO_15min.csv'

    def __call__(self):
        return self.lee_medidas()

    def lee_medidas(self):
        if self.QMin:
            print('Procesando data 15minutal')
            return self.lee_medidas_qmin()
        else:
            print('Procesando data Horaria')
            return self.lee_medidas_med()

    def lee_medidas_med(self):
        excel = self.carpeta / f'Medidas_horarias_{self.anio}{self.mes}.xlsb'
        med = self.carpeta / 'Medidas.csv'
        vb_excel = 'ExcelToCsv.vbs'
        if (med).exists():
            df = self.filtra_medidas(med)
        else:
            vbs = crea_archivo_vbscript()
            with tempfile.NamedTemporaryFile(
                    suffix=".vbs", delete=False) as temp_file:
                temp_filename = temp_file.name
                temp_file.write(vbs.encode())

            call(['cscript.exe', temp_filename, str(excel), str(med), "1"])
            temp_file.close()
            df = filtra_medidas(med)

        df_out = pd.melt(df,
                         id_vars=['clave', 'Valores'],
                         value_vars=df.columns[6:],
                         var_name='fecha')
        df2 = df_out.pivot_table(
            index=['clave', 'fecha'],
            columns='Valores').reset_index()
        df2.columns = df2.columns.to_flat_index()
        df2.columns = ['clave', 'fecha', 'Físico__kWh', ]

        df2['fecha'] = df2['fecha'].astype(int)
        df2 = df2.sort_values(by=['clave', 'fecha'])
        df2 = df2.pivot_table(
            columns='clave',
            index='fecha',
            values='Físico__kWh'
        )
        df2, med_p = self.entrega_medidas(df2)

        return df2, med_p

    def lee_medidas_qmin(self):
        med = self.carpeta / 'Medidas_15min.parquet'

        if (med).exists():
            df = pl.read_parquet(med)
            filtrado = df.filter(pl.col("propietario").str.contains("CGE_C"))
            df = filtrado.to_pandas()
        else:
            df = self.filtra_qmin()

        df = self.pasar_min_a_hora(df)

        df2 = df.pivot_table(
            index=['hora_del_mes'],
            columns='clave',
            values='MedidaHoraria')

        if 'GLINARES' in df2.columns:
            df2 = df2.drop(['GLINARES'], axis=1)

        df2, med_p = self.entrega_medidas(df2)
        return df2, med_p

    def entrega_medidas(self, df_in):
        df2 = df_in.copy()
        med_p = df_in.copy()

        med_p.fillna(0, inplace=True)
        df2['Total'] = df2.sum(axis=1)

        for c in med_p.columns.tolist():
            med_p[c] = med_p[c] / df2['Total']

        df2.to_excel(self.pth_out / f'{self.anio}{self.mes}_Medidas.xlsx')
        med_p.to_excel(self.pth_out / f'{self.anio}{self.mes}_Medidas_v2.xlsx')

        print('Proceso Medidas terminado')
        return df2, med_p

    def filtra_medidas(self, csv_file):
        df = pd.read_csv(csv_file, encoding='latin', header=1)

        df = df.loc[df['propietario'] == 'CGE_C']
        df = df.loc[df['Valores'] == 'Físico__kWh']
        df = df.loc[df['Tipo_Medida'].isin(['L', 'L_D'])]
        return df

    def filtra_qmin(self, borrar=True):
        # con polars es 3 a 4 veces más rápido
        patoolib.extract_archive(str(self.carpeta / self.ar_zip),
                                 outdir=str(self.carpeta),
                                 verbosity=1,
                                 interactive=False)
        df = pl.read_csv(self.carpeta / self.ar_csv, ignore_errors=True)
        df = df.drop(['nro_med', 'pnudo', 'nro_lt', 'clave_LT',
                      'MedidaHoraria2', 'Zona'])
        #Sólo quedar con L y L_D
        #df = df.loc[df['tipo1'].isin(['L', 'L_D'])]
        df = df.filter(df["tipo1"].is_in(["L", "L_D"]))
        df.write_parquet(self.carpeta / 'Medidas_15min.parquet')
        if borrar:
            csv = self.carpeta / self.ar_csv
            csv.unlink()
        # Filtrar la columna que contiene el string "CGE_C"
        filtrado = df.filter(pl.col("propietario").str.contains("CGE_C"))
        return filtrado.to_pandas()

    def pasar_min_a_hora(self, med):
        df = med.copy()
        f_inicial = pd.to_datetime(f'20{self.anio}-{self.mes}-01 00:00:00')
        intervalo = pd.Timedelta(minutes=15)
        df['Cuarto de Hora'] = f_inicial + (
                    df['Cuarto de Hora'] - 1) * intervalo
        df['fecha'] = df['Cuarto de Hora'].dt.date
        df['hora'] = df['Cuarto de Hora'].dt.hour
        cols = ['nombre_barra', 'clave', 'fecha', 'hora',
                'propietario', 'descripcion', 'tipo1']

        df = df.groupby(cols, as_index=False).agg({'MedidaHoraria': 'sum'})

        df['fecha'] = pd.to_datetime(df['fecha'])
        df['hora_del_mes'] = (df['fecha'].dt.day - 1) * 24 + df['hora']
        df['hora_del_mes'] = df['hora_del_mes'] + 1
        return df

def crea_archivo_vbscript(crear=False):
    vbscript = """if WScript.Arguments.Count < 3 Then
        WScript.Echo "Please specify the source and the destination files. Usage: ExcelToCsv <xls/xlsx source file_resumen> <csv destination file_resumen> <worksheet number (starts at 1)>"
        Wscript.Quit
    End If

    csv_format = 6

    Set objFSO = CreateObject("Scripting.FileSystemObject")

    src_file = objFSO.GetAbsolutePathName(Wscript.Arguments.Item(0))
    dest_file = objFSO.GetAbsolutePathName(WScript.Arguments.Item(1))
    worksheet_number = CInt(WScript.Arguments.Item(2))

    Dim oExcel
    Set oExcel = CreateObject("Excel.Application")

    Dim oBook
    Set oBook = oExcel.Workbooks.Open(src_file)
    oBook.Worksheets(worksheet_number).Activate

    oBook.SaveAs dest_file, csv_format

    oBook.Close False
    oExcel.Quit
        """;

    if crear:
        f = open('ExcelToCsv.vbs', 'bw')
        f.write(vbscript.encode('utf-8'))
        f.close()
    else:
        return vbscript

def descomprime(año, mes, proceso, carpeta, QMin=True):
    if proceso == 'pre':
        ind = 'P'
    else:
        ind = 'D'

    if QMin:
        file_zip = '03-Bases-de-Datos'
        archivo_zip = f'Balance_Valorizado_{año}{mes}_Data_VALORIZADO_15min.zip'
        #archivo_zip = f'RETIROS_{año}{mes}_15min.zip'
    else:
        file_zip = '02-Antecedentes-de-Calculo'
        archivo_zip = f'Medidas_horarias_{año}{mes}.xlsb'

    archivos = {
        '01-Resultados': [      # 01-Resultados-1
            f'Balance_{año}{mes}_B{ind}01.xlsm',
            f'Pago_Sobrecostos_{año}{mes}_',
            'Precio_estabilizado_'
        ],
        'Balance_SSCC_': [      # Balance_SSCC_
            f'Pagos_Retiros',
            f'11_PAGOS_BESS_{año}{mes}_{proceso}.xlsx'
        ],
        file_zip: [ archivo_zip ]
    }
    carpeta = Path(carpeta)
    for archivo, file in archivos.items():
        for f in file:
            archivo_desc = carpeta / f
            if not archivo_desc.exists():
                ar_zip = carpeta / archivo
                print(f'Descomprimiendo {archivo}, en '
                      f'{carpeta.glob(f'{archivo}*.zip')}')
                ar = list(carpeta.glob(f'{archivo}*.zip'))[0]
                zfile = ReadZipfile(ar)
                if f == 'Precio_estabilizado_':
                    files_desc = [x.split('/')[-1] for x in zfile.list_file() \
                                  if 'Precio_estabilizado_' in x]
                elif f == 'Pagos_Retiros':
                    files_desc = [x for x in zfile.list_file() if \
                                  'Pagos_Retiros' in x]
                elif f == f'Pago_Sobrecostos_{año}{mes}_':
                    files_desc = [x.split('/')[-1] for x in zfile.list_file() \
                                  if f'Pago_Sobrecostos_{año}{mes}_' in x]
                else:
                    files_desc = [f]

                for fi in files_desc:
                    archivo_desc = carpeta / fi
                    if not archivo_desc.exists():
                        zfile.read_zip_file(
                            file=fi,
                            where=carpeta,
                            delete_zip=False)

def procesa_b_energia(año, mes, proceso, carpeta):
    """
    Script para conocer el balance físico y monetario de la energia

    Configuración de parámetros, ejemplo:
        año = '23'
        mes = '04'
        proceso = 'def'
        carpeta_archivo = Path(r'C:\\Users\\<USER>\\Desktop\\Fact_clientes_libres
        \\Abril')
    """
    pth_out= carpeta / 'Procesados'
    pth_out.mkdir(exist_ok=True)

    # Selecciona los datos de la energi
    pre_def = 'D' if proceso == 'def' else 'P'
    archivo_benergia = f'Balance_{año}{mes}_B{pre_def}01.xlsm'
    path_archivo = carpeta / archivo_benergia

    df = pd.read_excel(path_archivo, sheet_name='TD_EMPRESAS', usecols='A:J',
                       index_col=None)

    cgec = df.loc[df['Empresa'].str.contains('CGE_C', na=False)]
    cgec = cgec.loc[cgec['tipo1'].isin(['L_D', 'L'])]

    cgec.to_excel(pth_out / f'{año}{mes}{proceso}_benergia.xlsx', index=False)

    print('Proceso B.Energia terminado')
    return cgec

def procesa_m_tec(año, mes, proceso, carpeta):
    pth_out = carpeta / 'Procesados'
    pth_out.mkdir(exist_ok=True)

    archivo_sobrecosto = f'Pago_Sobrecostos_{año}{mes}_'
    archivo_sobrecosto = carpeta.glob(f'{archivo_sobrecosto}*')
    archivo_sobrecosto = list(archivo_sobrecosto)[0]
    path_archivo = carpeta / archivo_sobrecosto

    df = pl.read_excel(path_archivo, sheet_name='PAGO_RETIRO')

    cgec = df.filter(pl.col('Suministrador') == 'CGE_C')
    cgec = cgec.to_pandas()

    cgec.to_excel(pth_out / f'{año}{mes}{proceso}_min_tec.xlsx', index=False)
    print('Proceso MTec terminado')
    return cgec

def procesa_sscc_pl(año, mes, proceso, carpeta):
    pth_out = carpeta / 'Procesados'
    pth_out.mkdir(exist_ok=True)

    ar_sscc_1 = \
        f'4_REMUNERACIÓN_SC_CO_CCA_Y_Pagos_Retiros_{año}{mes}_{proceso}.xlsx'
    ar_sscc_2 = \
        f'9_Pagos_Retiros_CRA_REA_CO_ERNC_Subastas_{año}{mes}_{proceso}.xlsx'
    ar_sscc_3 = f'11_PAGOS_BESS_{año}{mes}_{proceso}.xlsx'
    pre_def = 'D' if proceso == 'def' else 'P'
    ar_sscc_4 = f'Balance_{año}{mes}_B{pre_def}01.xlsm'

    df_salida = {}
    for i, ar in enumerate([ar_sscc_1, ar_sscc_2, ar_sscc_3, ar_sscc_4]):
        if i == 2:
            print('\tArchivo Bess no revisado')
            continue
        if i == 3:
            opt = {'sheet_name': 'SOBRECOSTOS PARTIDA DETENCIÓN',
                   'engine': 'calamine'
                   }
            empr = 'Empresa'
            ind = 2

        else:
            opt = {'sheet_name': 'PAGO_RETIRO',
                   'engine': 'calamine'
                   }
            empr = 'Suministrador'
            ind = ''

        p_arch = carpeta / ar
        cgec = lee_polars(p_arch, opt, ind)
        cgec = cgec.loc[cgec[empr].str.contains('CGE_C')]
        df_salida[i] = cgec
        cgec.to_excel(pth_out / f'{año}{mes}{proceso}_sscc_{i+1}.xlsx',
                      index=False)

    print('Proceso SSCC terminado')
    return df_salida

def lee_polars(p_arch, opt, ind = ''):
    df = pl.read_excel(p_arch, **opt)
    cgec = df.to_pandas()
    if ind != '':
        header_names = cgec.iloc[1].tolist()
        cgec.columns = header_names
        cgec = cgec.drop(cgec.index[:ind])
    return cgec

def procesa_p_estabilizado(año, mes, proceso, carpeta):
    pth_out = carpeta / 'Procesados'
    pth_out.mkdir(exist_ok=True)

    ar_pest = []
    df_caso = {}
    for archivo in carpeta.glob('*.*'):
        if archivo.name.startswith('Precio_estabilizado_'):
            ar_pest.append(archivo.name)
        else:
            continue

    opt = {'sheet_name': 'COMPENSACION', 'usecols': 'A:E', 'index_col': None}
    for i, ar in enumerate(ar_pest):
        if '_14T' in ar:
            ncol = '14T'
        elif 'RE131' in ar:
            ncol = 'RE131'
        elif 'RE828' in ar:
            ncol = 'RE828'
        else:
            ncol = 'PE'

        p_arch = carpeta / ar
        df = pd.read_excel(p_arch, **opt)

        cgec = df.loc[df['Suministrador'].str.contains('CGE_C')]
        cgec = cgec.rename(columns={'Compensacion [$]': ncol})
        df_caso[ncol] = cgec

    df_fin = pd.DataFrame()
    for k, v in df_caso.items():
        df_fin[k] = v[k]

    df_fin['Total'] = df_fin.sum(axis=1)
    df_fin.set_index(df_caso[k]['Hora Mensual'], inplace=True)
    df_fin.to_excel(pth_out / f'{año}{mes}{proceso}_precio_est.xlsx',
                    index=True)
    print('Proceso P_Estabilizado terminado')
    return df_fin

def compila_mt(df_e, df_mt, df_sct, carpeta):
    pth_out = carpeta / 'Procesados'
    pth_out.mkdir(exist_ok=True)

    df_e['pr_e'] = df_e['Fisico_kWh'] / df_e['Fisico_kWh'].sum()

    mtpc = df_mt.groupby(['clave', 'Retiro', 'Barra', 'Tipo', 'Suministrador'],
                         as_index=False).agg({'Suma de Pago': 'sum'})
    mtpc = mtpc.merge(df_e[['clave', 'pr_e']], on=['clave'], how='left')

    # SSCC
    SC_CO_CCA = df_sct[0].groupby(
        ['clave']).agg({'Suma de Pago': 'sum'}).rename(
        columns={'Suma de Pago': 'SSCC'})
    CRA_REA_CO = df_sct[1].groupby(
        ['clave']).agg({'Suma de Pago': 'sum'}).rename(
        columns={'Suma de Pago': 'SSCC'})
    SC_CO_CCA['SSCC'] += CRA_REA_CO['SSCC']
    SC_CO_CCA.reset_index(inplace=True)

    mtpc = mtpc.merge(SC_CO_CCA, on=['clave'], how='left')

    # No se ha implementado el BESS

    # Mínimo Técnico, prorrata
    mtpc['SSCC_MT'] = mtpc['Suma de Pago'] \
                 + mtpc['pr_e'] * float(df_sct[3]['PAGA'].values[0])
    mtpc.to_csv(pth_out / '1_MT_PC.csv', index=False)

    return mtpc

def calc_pest_hor(df_pest, df_med, carpeta):
    pth_out = carpeta / 'Procesados'
    pth_out.mkdir(exist_ok=True)

    df = df_med.copy()

    for c in df.columns.tolist():
        df[c] = df[c].values * df_pest['Total'].values

    dff = pd.DataFrame(columns=['Pr_Est'], data=df.sum().values,
                      index=df.columns.values).reset_index()
    dff.columns = ['clave', 'Pr_Est']
    dff.to_csv(pth_out / '2_pest_hor.csv')
    return dff

def tabla_resumen(df_mt, df_horario, carpeta):
    pth_out = carpeta / 'Procesados'
    pth_out.mkdir(exist_ok=True)

    df_resumen = df_mt.merge(df_horario, on="clave", how="left")
    df_resumen.drop(
        columns=[
            'Tipo', 'Suministrador', 'Suma de Pago'
        ], inplace=True)

    df_resumen.to_excel(pth_out / "_resumen.xlsx", index=False)

    return df_resumen

def proceso_sscc(año, mes, proceso, carpeta_archivo):
    # Descomprimiendo archivos
    print('Descomprimiendo archivos')
    descomprime(año, mes, proceso, carpeta_archivo)

    # Realiza cálculos
    print('Realizando calculos')
    be = procesa_b_energia(año, mes, proceso, carpeta_archivo)
    mt = procesa_m_tec(año, mes, proceso, carpeta_archivo)
    sc = procesa_sscc_pl(año, mes, proceso, carpeta_archivo)
    pest = procesa_p_estabilizado(año, mes, proceso, carpeta_archivo)

    # Analizando archivo de Medidas
    # med[0] -> medidas   med[1] -> prorrata horaria
    print('Analizando archivo de Medidas')
    med = LeeMedidas(año, mes, carpeta_archivo)()

    # Prorratas y tabla final, Realizando calculos finales
    print('Realizando calculos finales')
    sscc = compila_mt(be, mt, sc, carpeta_archivo)
    pr_est_h = calc_pest_hor(pest, med[1], carpeta_archivo)
    tfin = tabla_resumen(sscc, pr_est_h, carpeta_archivo)

    print('******** Proceso terminado ********')

def procesa_cmg_def_n_2(folder, agno, mes):
    final_file = f'CMg_{agno}_{mes}_def.parquet'
    file_usd = f'USD_{agno}{mes}_def.xlsx'

    folder = Path(folder)
    # agno y mes deben ser strings de largo 2
    cmg = f'cmg{agno}{mes}_15minutal.csv'

    df = pl.read_csv(folder / cmg, separator=";", has_header=True,
                     ignore_errors=False, decimal_comma=False,
                     skip_rows=0, encoding="latin1")
    df_fin = df.to_pandas()
    df_fin['FECHA'] = pd.to_datetime(df_fin['FECHA'], format='%Y%m%d')
    df_fin['FECHA'] = (
            df_fin['FECHA']
            + pd.to_timedelta(df_fin['HORA'], unit='h')
            + pd.to_timedelta(df_fin['MINUTO'], unit='m'))
    df_fin = df_fin.drop(['MINUTO'], axis=1)
    df_fin = df_fin.drop(['HORA'], axis=1)

    df_fin.rename(columns={'FECHA': 'Fecha', 'BARRA': 'Barra'}, inplace=True)
    df_fin.to_parquet(folder / final_file)
    cols = ['Fecha', 'USD']
    barra = 'C.NAVIA_______110'
    df_usd = df_fin.loc[df_fin['Barra'] == barra][cols]
    df_usd.to_excel(folder / file_usd, index=False)

    print('Fin del proceso...')
    return df_fin

#%%
if __name__ == '__main__':
    #%%
    carpeta = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\General CGE Cx\Facturación PPAs\Cálculo Facturación\2024\202412\09. IVT-Def')
    archivo = 'Balance_SSCC_'

    descomprime(año='24', mes='12', proceso='def', carpeta=carpeta)

    #%%
    folder = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\General CGE Cx\Facturación PPAs\Cálculo Facturación\2025\202502\09. IVT-Def')
    df = procesa_cmg_def_n_2(folder, '25', '01')
    #%%
    df


