import pandas as pd
import polars as pl
import pyxlsb
from pathlib import Path
import modin.pandas as mpd
import numpy as np
import ray


def read_xlsb_by_rows(file_path, sheet_name=0, batch_size=10000):
    """
    Lee un archivo XLSB por lotes de filas usando pyxlsb directamente.
    Permite procesar archivos grandes sin cargarlos completamente en memoria.
    """
    rows = []
    headers = None

    with pyxlsb.open_workbook(file_path) as wb:
        with wb.get_sheet(sheet_name) as sheet:
            # Leer los headers primero
            for row in sheet.rows():
                headers = [item.v for item in row]
                break

            # Leer el resto de los datos por lotes
            current_batch = []
            for i, row in enumerate(sheet.rows(), start=1):
                if i == 1:  # Skip header row since we already got it
                    continue

                current_batch.append([item.v for item in row])

                if len(current_batch) >= batch_size:
                    # Convertir el lote actual a DataFrame
                    df_batch = pd.DataFrame(current_batch, columns=headers)
                    yield df_batch
                    current_batch = []

            # Procesar el último lote si existe
            if current_batch:
                df_batch = pd.DataFrame(current_batch, columns=headers)
                yield df_batch


def read_xlsb_to_polars(file_path, sheet_name=0):
    """
    Lee el archivo XLSB y lo convierte a Polars para análisis eficiente.
    """
    with pyxlsb.open_workbook(file_path) as wb:
        with wb.get_sheet(sheet_name) as sheet:
            data = []
            for row in sheet.rows():
                data.append([item.v for item in row])

    # Convertir directamente a Polars
    return data
    #return pl.DataFrame(data[1:], schema=data[0])


def process_large_xlsb(file_path, sheet_name=0, batch_size=10000):
    """
    Ejemplo de cómo procesar un archivo XLSB grande por lotes.
    """
    total_rows = 0
    running_sum = 0

    for batch_df in read_xlsb_by_rows(file_path, sheet_name, batch_size):
        # Ejemplo de procesamiento por lotes
        total_rows += len(batch_df)
        # Aquí puedes agregar tu lógica de procesamiento
        print(
            f"Procesado lote de {len(batch_df)} filas. Total hasta ahora: {total_rows}")


def read_xlsb_with_modin(file_path, sheet_name=0):
    """
    Lee un archivo XLSB usando Modin para procesamiento paralelo.
    Nota: Modin no soporta directamente XLSB, así que primero convertimos a CSV.
    """
    # Inicializar Ray (el backend por defecto de Modin)
    ray.init()

    # Primero convertimos XLSB a un formato que Modin pueda leer eficientemente
    print("Convirtiendo XLSB a formato intermedio...")

    # Leemos con pyxlsb y convertimos a CSV temporal
    temp_csv = "temp_data.csv"
    with pyxlsb.open_workbook(file_path) as wb:
        with wb.get_sheet(sheet_name) as sheet:
            # Convertir datos a DataFrame regular de pandas
            data = []
            for row in sheet.rows():
                data.append([item.v for item in row])

            pd.DataFrame(data[1:], columns=data[0]).to_csv(temp_csv,
                                                           index=False)

    # Ahora leemos con Modin para procesamiento paralelo
    print("Leyendo datos con Modin...")
    df_modin = mpd.read_csv(temp_csv)

    # Limpieza
    import os
    os.remove(temp_csv)

    return df_modin


def process_with_modin(df_modin):
    """
    Ejemplo de procesamiento usando Modin.
    Las operaciones serán ejecutadas en paralelo automáticamente.
    """
    # Ejemplos de operaciones que se benefician del procesamiento paralelo

    # Estadísticas descriptivas
    stats = df_modin.describe()

    # Agrupación y agregación
    grouped = df_modin.groupby(df_modin.columns[0]).agg(
        ['mean', 'sum', 'count'])

    # Filtrado
    filtered = df_modin[df_modin > df_modin.mean()]

    return stats, grouped, filtered