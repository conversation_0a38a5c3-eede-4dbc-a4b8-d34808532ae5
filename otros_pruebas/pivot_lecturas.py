"""
Pivoteo de Lecturas de Medidores

Este script lee un archivo Excel o CSV con lecturas de medidores y lo pivotea
para tener fechas e instalaciones como filas, y los diferentes tipos de medida
(kWh, kW1, kW2, kVR) como columnas.

Para lecturas duplicadas dentro del mismo mes, se conserva únicamente la lectura
más reciente y se descartan las demás.

Autor: CGE
Fecha: 2023-2024
"""

import polars as pl
import os
from pathlib import Path
import pandas as pd  # For Excel export
import io
from datetime import datetime, timedelta

def convert_column_types(df):
    """
    Convierte explícitamente los tipos de datos para las columnas problemáticas.

    Args:
        df (pl.DataFrame): DataFrame a convertir

    Returns:
        pl.DataFrame: DataFrame con tipos de datos convertidos
    """
    # Lista de índices de columnas problemáticas
    problematic_columns = [13, 14, 15, 21, 23, 25, 27, 28, 30, 31, 33, 37]

    # Verificar que el DataFrame tiene suficientes columnas
    if df.shape[1] <= max(problematic_columns):
        print(f"Advertencia: El DataFrame tiene {df.shape[1]} columnas, pero se esperaban al menos {max(problematic_columns) + 1}.")
        return df

    # Procesar cada columna problemática
    for col_idx in problematic_columns:
        if col_idx < df.shape[1]:
            col_name = df.columns[col_idx]
            col_dtype = df.dtypes[col_idx]

            # Si la columna es de tipo string, primero limpiar los valores
            if str(col_dtype) == "String":
                try:
                    # Primero reemplazar strings vacíos con None y luego reemplazar comas
                    df = df.with_columns([
                        pl.when(pl.col(col_name) == "").then(pl.lit(None))
                          .otherwise(pl.col(col_name).str.replace(",", ""))
                          .alias(col_name)
                    ])

                    # Ahora convertir a Float64
                    df = df.with_columns([
                        pl.col(col_name).cast(pl.Float64)
                    ])
                    print(f"Columna {col_idx} ({col_name}) convertida de String a Float64 con manejo de strings vacíos y comas")
                except Exception as e:
                    print(f"Error al convertir columna {col_idx} ({col_name}): {e}")
            else:
                try:
                    # Para otros tipos, intentar convertir directamente
                    df = df.with_columns([
                        pl.col(col_name).cast(pl.Float64)
                    ])
                    print(f"Columna {col_idx} ({col_name}) convertida a Float64")
                except Exception as e:
                    print(f"Error al convertir columna {col_idx} ({col_name}): {e}")

    return df

def process_excel_file(file_path):
    """
    Read an Excel file_resumen with meter readings and pivot it to have dates and installation numbers as rows,
    and different measurement types (kWh, kW1, kW2, kVR) as columns.

    For duplicate readings within the same month, only the newest reading is kept.

    Args:
        file_path (str): Path to the Excel file_resumen

    Returns:
        pl.DataFrame: Pivoted dataframe
    """
    # Check if file_resumen exists
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"El archivo {file_path} no existe")

    # Determine file_resumen type based on extension
    file_ext = Path(file_path).suffix.lower()

    # Read the file_resumen based on its extension
    try:
        if file_ext in ['.xlsx', '.xls']:
            # For Excel file_resumen
            df = pl.read_excel(file_path)
            # Convert problematic column types
            df = convert_column_types(df)
        elif file_ext == '.csv':
            # For CSV file_resumen
            df = pl.read_csv(file_path)
        else:
            # Try as tab-separated file_resumen
            df = pl.read_csv(file_path, separator='\t')
    except Exception as e:
        print(f"Error al leer el archivo: {e}")
        # Try alternative formats as fallback
        try:
            if file_ext in ['.xlsx', '.xls']:
                # Try as CSV if Excel reading failed
                df = pl.read_csv(file_path, separator='\t')
            else:
                # Try as Excel if CSV reading failed
                df = pl.read_excel(file_path)
                # Convert problematic column types
                df = convert_column_types(df)
        except Exception as e2:
            raise ValueError(f"No se pudo leer el archivo. Intenté varios formatos pero fallaron: {e}, {e2}")

    # Check if required columns exist
    required_columns = ["Instal.", "Fe.lectura", "UM", "Num.", "LectCont"]
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        raise ValueError(f"Faltan columnas requeridas en el archivo: {', '.join(missing_columns)}")

    # Create a new column to identify the measurement type
    # Based on the 'UM' column and 'Num.' column
    df = df.with_columns(
        pl.when(pl.col("UM") == "kWh").then(pl.lit("kWh"))
        .when((pl.col("UM") == "kW") & (pl.col("Num.") == "002")).then(pl.lit("kW_medida"))
        .when((pl.col("UM") == "kW") & (pl.col("Num.") == "004")).then(pl.lit("kW_HP"))
        .when(pl.col("UM") == "KVR").then(pl.lit("kVR"))
        .otherwise(pl.col("UM"))
        .alias("TipoMedida")
    )

    # Print unique measurement types for debugging
    unique_types = df.select("TipoMedida").unique().to_series().to_list()
    print(f"Tipos de medida encontrados: {unique_types}")

    # Extract month and year information
    df = df.with_columns([
        pl.col("Fe.lectura").dt.month().alias("Mes"),
        pl.col("Fe.lectura").dt.year().alias("Año"),
        pl.col("Fe.lectura").dt.day().alias("Dia")
    ])

    # Group by installation, month, year, and measurement type and keep only the newest reading
    print("Eliminando lecturas duplicadas dentro del mismo mes...")

    # First, create a unique identifier for each group (Instal, Mes, Año, TipoMedida)
    # Note: We don't include the day in the group ID to ensure we group by month
    df = df.with_columns(
        (pl.col("Instal.").cast(pl.Utf8) + "_" +
         pl.col("Mes").cast(pl.Utf8) + "_" + 
         pl.col("Año").cast(pl.Utf8) + "_" + 
         pl.col("TipoMedida")).alias("GrupoID")
    )

    # Get the latest date for each group
    # This ensures we keep only the newest reading per month for each installation and measurement type
    latest_dates = df.group_by(["GrupoID"]).agg(
        pl.col("Fe.lectura").max().alias("FechaMax")
    )

    # Join back to get only the rows with the latest date in each group
    df = df.join(latest_dates, on="GrupoID")
    df = df.filter(pl.col("Fe.lectura") == pl.col("FechaMax"))

    # Drop temporary columns
    df = df.drop(["GrupoID", "FechaMax"])

    print(f"Filas después de eliminar duplicados: {df.shape[0]}")

    # Pivot the table
    try:
        pivoted_df = df.pivot(
            index=["Instal.", "Fe.lectura", "Año", "Mes"],
            columns="TipoMedida",
            values="LectCont"
        )

        # Sort by installation and date
        pivoted_df = pivoted_df.sort(["Instal.", "Fe.lectura", "Año", "Mes"])

        return pivoted_df
    except Exception as e:
        print(f"Error al pivotar la tabla: {e}")
        print("Mostrando las primeras filas del DataFrame para diagnóstico:")
        print(df.head())
        raise

def main():
    # Get the file_resumen path from user input
    file_path = input("Ingrese la ruta del archivo Excel: ")

    try:
        print(f"Procesando archivo: {file_path}")
        # Process the file_resumen
        result_df = process_excel_file(file_path)

        # Display the result
        print("\nResultado:")
        print(result_df)

        # Ask if user wants to save the result
        save_option = input("\n¿Desea guardar el resultado? (s/n): ").lower()
        if save_option == 's':
            output_path = input("Ingrese la ruta para guardar el archivo: ")

            # Determine file_resumen format based on extension
            path_obj = Path(output_path)
            extension = path_obj.suffix.lower()

            if extension == '.xlsx':
                # Save as Excel
                try:
                    # Convert to pandas for Excel export (polars doesn't have native Excel writer)
                    pandas_df = result_df.to_pandas()
                    pandas_df.to_excel(output_path, index=False)
                    print(f"Archivo Excel guardado en: {output_path}")
                except ImportError:
                    print("No se pudo guardar como Excel. Pandas no está instalado.")
                    print("Guardando como CSV en su lugar...")
                    result_df.write_csv(f"{path_obj.with_suffix('.csv')}")
                    print(f"Archivo CSV guardado en: {path_obj.with_suffix('.csv')}")
            elif extension == '.csv':
                # Save as CSV
                result_df.write_csv(output_path)
                print(f"Archivo CSV guardado en: {output_path}")
            else:
                # Default to CSV if extension is not recognized
                new_path = f"{path_obj.with_suffix('.csv')}"
                result_df.write_csv(new_path)
                print(f"Extensión no reconocida. Archivo CSV guardado en: {new_path}")

    except FileNotFoundError as e:
        print(f"Error: {e}")
    except Exception as e:
        print(f"Error durante el procesamiento: {e}")
        import traceback
        traceback.print_exc()

def create_sample_file(output_path):
    """
    Create a sample Excel file_resumen with the expected format for testing.
    Includes examples of duplicate readings within the same month.

    Args:
        output_path (str): Path where the sample file_resumen will be saved
    """
    # Sample data similar to the example in the issue description
    # Includes duplicate readings within the same month (with different dates)
    data = """Instal.\tNº LógNum\tHLect\tEquipo\tAparato\tNum.\tML\tUM\tDescrip.breve\tFe.lectura\tLectCont
101918201\t000000001003587044\t16:50\t5019922463\t62082192\t001\t01\tkWh\tLectura periódica\t27-03-2025\t57,548.000
101918201\t000000001003587045\t16:50\t5019922463\t62082192\t002\t01\tkW\tLectura periódica\t27-03-2025\t108.606
101918201\t000000001003587047\t16:50\t5019922463\t62082192\t004\t01\tkW\tLectura periódica\t27-03-2025\t55.647
101918201\t000000001003587049\t16:50\t5019922463\t62082192\t006\t01\tKVR\tLectura periódica\t27-03-2025\t11,208.000
101918201\t000000001003587044\t10:30\t5019922463\t62082192\t001\t01\tkWh\tLectura periódica\t15-03-2025\t57,200.000
101918201\t000000001003587045\t10:30\t5019922463\t62082192\t002\t01\tkW\tLectura periódica\t15-03-2025\t107.500
101918201\t000000001003587047\t10:30\t5019922463\t62082192\t004\t01\tkW\tLectura periódica\t15-03-2025\t55.000
101918201\t000000001003587049\t10:30\t5019922463\t62082192\t006\t01\tKVR\tLectura periódica\t15-03-2025\t11,000.000
101918201\t000000001003587044\t13:43\t5019922463\t62082192\t001\t01\tkWh\tLectura periódica\t26-02-2025\t56,793.000
101918201\t000000001003587045\t13:43\t5019922463\t62082192\t002\t01\tkW\tLectura periódica\t26-02-2025\t106.868
101918201\t000000001003587047\t11:12\t5019922463\t62082192\t004\t01\tkW\tLectura periódica\t26-02-2025\t55.647
101918201\t000000001003587049\t13:43\t5019922463\t62082192\t006\t01\tKVR\tLectura periódica\t26-02-2025\t10,999.000
101918201\t000000001003587044\t09:15\t5019922463\t62082192\t001\t01\tkWh\tLectura periódica\t10-02-2025\t56,500.000
101918201\t000000001003587045\t09:15\t5019922463\t62082192\t002\t01\tkW\tLectura periódica\t10-02-2025\t106.000
101918201\t000000001003587047\t09:15\t5019922463\t62082192\t004\t01\tkW\tLectura periódica\t10-02-2025\t55.000
101918201\t000000001003587049\t09:15\t5019922463\t62082192\t006\t01\tKVR\tLectura periódica\t10-02-2025\t10,800.000"""

    # Create a DataFrame from the sample data
    df = pd.read_csv(io.StringIO(data), sep='\t')

    # Save as Excel
    path_obj = Path(output_path)
    if not path_obj.suffix:
        # Add .xlsx extension if not provided
        output_path = f"{output_path}.xlsx"

    df.to_excel(output_path, index=False)
    print(f"Archivo de muestra creado en: {output_path}")

    return output_path

def analyze_excel_file(file_path):
    """
    Analyze an Excel file_resumen and provide information about its structure and content.

    Args:
        file_path (str): Path to the Excel file_resumen

    Returns:
        dict: Dictionary containing analysis results
    """
    # Check if file_resumen exists
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"El archivo {file_path} no existe")

    # Get file_resumen size in MB
    file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
    print(f"Tamaño del archivo: {file_size_mb:.2f} MB")

    # Read Excel file_resumen info without loading all data
    excel_info = pd.ExcelFile(file_path)
    sheet_names = excel_info.sheet_names
    print(f"Hojas en el archivo: {sheet_names}")

    # Initialize results dictionary
    results = {
        "file_path": file_path,
        "file_size_mb": file_size_mb,
        "sheet_names": sheet_names,
        "sheets_info": {}
    }

    # Analyze each sheet
    for sheet in sheet_names:
        print(f"\nAnalizando hoja: {sheet}")

        # Read first few rows to get column info
        df_sample = pd.read_excel(file_path, sheet_name=sheet, nrows=5)

        # Get row count (may be slow for large files)
        try:
            row_count = len(pd.read_excel(file_path, sheet_name=sheet, usecols=[0]))
        except:
            row_count = "No se pudo determinar (archivo muy grande)"

        # Get column info
        columns = df_sample.columns.tolist()
        column_types = {col: str(df_sample[col].dtype) for col in columns}

        # Get sample data
        sample_data = df_sample.head(5).to_dict(orient='records')

        # Store sheet info
        results["sheets_info"][sheet] = {
            "row_count": row_count,
            "column_count": len(columns),
            "columns": columns,
            "column_types": column_types,
            "sample_data": sample_data
        }

        print(f"  Columnas: {len(columns)}")
        print(f"  Filas: {row_count}")
        print(f"  Primeras columnas: {columns[:5] if len(columns) > 5 else columns}")

    return results

def show_help():
    """Display help information about the script."""
    help_text = """
    AYUDA - Pivoteo de Lecturas
    ===========================

    Este script lee un archivo Excel o CSV con lecturas de medidores y lo pivotea
    para tener fechas e instalaciones como filas, y los diferentes tipos de medida
    (kWh, kW1, kW2, kVR) como columnas.

    Para lecturas duplicadas dentro del mismo mes, se conserva únicamente la lectura
    más reciente y se descartan las demás.

    Formato esperado del archivo:
    - Debe contener las columnas: Instal., Fe.lectura, UM, Num., LectCont
    - Los valores de UM pueden ser: kWh, kW, KVR
    - Los valores de Num. para kW pueden ser: 002 (kW1) y 004 (kW2)
    - El formato de fecha en Fe.lectura debe ser DD-MM-YYYY

    Opciones:
    -h, --help     : Muestra esta ayuda
    -s, --sample   : Crea un archivo de muestra para pruebas
    -a, --analyze  : Analiza la estructura de un archivo Excel sin procesarlo
    -m, --multiple : Procesa múltiples archivos Excel y los concatena en uno solo

    Ejemplo de uso:
    python pivot_lecturas.py
    python pivot_lecturas.py -s archivo_muestra.xlsx
    python pivot_lecturas.py -a archivo_excel.xlsx
    python pivot_lecturas.py -m directorio
    python pivot_lecturas.py -m "directorio\\*.xlsx" consolidado.xlsx
    """
    print(help_text)

def process_multiple_excel_files(file_paths, output_path="consolidado.xlsx"):
    """
    Process multiple Excel files and concatenate their data.

    Args:
        file_paths (list): List of paths to Excel files
        output_path (str): Path to save the consolidated data

    Returns:
        pl.DataFrame: Consolidated dataframe
    """
    print(f"Procesando {len(file_paths)} archivos Excel...")

    # Initialize an empty dataframe to store the consolidated data
    consolidated_df = None

    # Process each file_resumen
    for i, file_path in enumerate(file_paths):
        print(f"\nProcesando archivo {i+1}/{len(file_paths)}: {file_path}")

        try:
            # Check if file_resumen exists
            if not os.path.exists(file_path):
                print(f"Error: El archivo {file_path} no existe. Saltando...")
                continue

            # Determine file_resumen type based on extension
            file_ext = Path(file_path).suffix.lower()

            if file_ext not in ['.xlsx', '.xls']:
                print(f"Error: El archivo {file_path} no es un archivo Excel. Saltando...")
                continue

            # Read the file_resumen
            if i == 0:
                # For the first file_resumen, read with headers
                df = pl.read_excel(file_path)
                # Convert problematic column types
                df = convert_column_types(df)
                print(f"Leído archivo con {df.shape[0]} filas y {df.shape[1]} columnas")

                # Store the consolidated data
                consolidated_df = df
            else:
                # For subsequent files, skip the header row
                df = pl.read_excel(file_path)
                # Convert problematic column types
                df = convert_column_types(df)
                # Skip the first row (header)
                df = df.slice(1, df.shape[0] - 1)
                print(f"Leído archivo con {df.shape[0]} filas y {df.shape[1]} columnas")

                # Concatenate with the consolidated data
                if consolidated_df is not None:
                    consolidated_df = pl.concat([consolidated_df, df])
                else:
                    consolidated_df = df

            print(f"Total acumulado: {consolidated_df.shape[0]} filas")

        except Exception as e:
            print(f"Error al procesar el archivo {file_path}: {e}")
            import traceback
            traceback.print_exc()

    # Save the consolidated data
    if consolidated_df is not None and consolidated_df.shape[0] > 0:
        print(f"\nGuardando datos consolidados en {output_path}...")

        try:
            # Convert to pandas for Excel export
            pandas_df = consolidated_df.to_pandas()
            pandas_df.to_excel(output_path, index=False)
            print(f"Archivo Excel guardado en: {output_path}")
        except Exception as e:
            print(f"Error al guardar el archivo consolidado: {e}")
            # Try saving as CSV as fallback
            try:
                csv_path = Path(output_path).with_suffix('.csv')
                consolidated_df.write_csv(csv_path)
                print(f"Archivo CSV guardado en: {csv_path}")
            except Exception as e2:
                print(f"Error al guardar como CSV: {e2}")
    else:
        print("No hay datos para guardar.")

    return consolidated_df

if __name__ == "__main__":
    import sys
    import glob

    # Check for command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            show_help()
        elif sys.argv[1] in ['-s', '--sample']:
            # Create sample file_resumen
            output_path = sys.argv[2] if len(sys.argv) > 2 else "muestra_lecturas.xlsx"
            sample_file = create_sample_file(output_path)

            # Ask if user wants to process the sample file_resumen
            process_sample = input(f"\n¿Desea procesar el archivo de muestra creado? (s/n): ").lower()
            if process_sample == 's':
                # Process the sample file_resumen
                try:
                    result_df = process_excel_file(sample_file)
                    print("\nResultado del procesamiento del archivo de muestra:")
                    print(result_df)
                except Exception as e:
                    print(f"Error al procesar el archivo de muestra: {e}")
        elif sys.argv[1] in ['-a', '--analyze']:
            # Analyze Excel file_resumen
            if len(sys.argv) > 2:
                file_path = sys.argv[2]
                try:
                    analyze_excel_file(file_path)
                except Exception as e:
                    print(f"Error al analizar el archivo: {e}")
            else:
                print("Error: Debe especificar la ruta del archivo a analizar.")
                print("Ejemplo: python pivot_lecturas.py -a archivo.xlsx")
        elif sys.argv[1] in ['-m', '--multiple']:
            # Process multiple Excel files
            if len(sys.argv) > 2:
                # Get the directory or file_resumen pattern
                path_pattern = sys.argv[2]

                # Get all Excel files matching the pattern
                if '*' in path_pattern:
                    # It's a pattern
                    file_paths = glob.glob(path_pattern)
                else:
                    # It's a directory
                    file_paths = glob.glob(f"{path_pattern}\\*.xlsx") + glob.glob(f"{path_pattern}\\*.xls")

                if file_paths:
                    print(f"Se encontraron {len(file_paths)} archivos Excel:")
                    for i, path in enumerate(file_paths):
                        print(f"{i+1}. {path}")

                    # Ask for confirmation
                    confirm = input("\n¿Desea procesar estos archivos? (s/n): ").lower()
                    if confirm == 's':
                        # Process the files
                        output_path = sys.argv[3] if len(sys.argv) > 3 else "consolidado.xlsx"
                        process_multiple_excel_files(file_paths, output_path)
                else:
                    print(f"No se encontraron archivos Excel en {path_pattern}")
            else:
                print("Error: Debe especificar la ruta del directorio o un patrón de archivos.")
                print("Ejemplo: python pivot_lecturas.py -m directorio")
                print("Ejemplo: python pivot_lecturas.py -m \"directorio\\*.xlsx\"")
        else:
            print(f"Opción desconocida: {sys.argv[1]}")
            print("Use -h o --help para ver las opciones disponibles.")
    else:
        # Normal execution
        # C:\Users\<USER>\OneDrive - Grupo CGE\2025\04_InformeClientes\Consumo Cliente Regulado Ejemplo.xlsx
        main()
