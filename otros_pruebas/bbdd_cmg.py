#%%
from pathlib import Path
import pandas as pd
import polars as pl    
import duckdb
import time
import re


def crea_rango(agno_i, mes_i, agno_f, mes_f):
    """
    Genera un rango de fechas en formato 'YYYY-MM' desde la fecha de inicio hasta la fecha de fin.

    Args:
        agno_i (str): Año de inicio.
        mes_i (str): Mes de inicio.
        agno_f (str): Año de fin.
        mes_f (str): Mes de fin.

    Yields:
        str: Fechas en formato 'YYYY-MM' dentro del rango.
    """
    a_aux = int(agno_i)
    mes_aux = int(mes_i)
    print(
        f"Rango inicial: {agno_i}-{mes_i.zfill(2)} hasta {agno_f}-{mes_f.zfill(2)}")

    while (a_aux < int(agno_f)) or (
            a_aux == int(agno_f) and mes_aux <= int(mes_f)):
        fecha_generada = f'{a_aux}-{str(mes_aux).zfill(2)}'
        # print(f"Generando fecha: {fecha_generada}")
        yield fecha_generada
        if mes_aux == 12:
            a_aux += 1
            mes_aux = 1

        else:
            mes_aux += 1


def get_cmg_barra(folder: Path, barra: str, date_i: str, date_f: str):
    """
    Extrae los datos de CMg para una barra específica en un rango de fechas.

    Args:
        folder (Path): Ruta a la carpeta base de datos.
        barra (str): Nombre de la barra a filtrar.
        date_i (str): Fecha de inicio en formato 'AAAA-MM'.
        date_f (str): Fecha de fin en formato 'AAAA-MM'.
        print (func, optional): Función para reportar mensajes de estado.
    """
    def recursive_load(rango_fechas, data):
        try:
            fecha = next(rango_fechas)
        except StopIteration:
            return data

        year, month = fecha.split('-')
        df_path = fder / f"CMg_{year[-2:]}_{month}_def.parquet"
        archivo = f"CMg_{year[-2:]}_{month}_def.parquet"


        if not df_path.exists():
            return recursive_load(rango_fechas, data)

        # Cargar y filtrar el archivo
        try:
            df = pl.read_parquet(df_path)
            barras = '|'.join([x.strip().upper() for x in barra.split(',')])
            df = df.filter(pl.col('Barra').str.to_uppercase().str.contains(barras))
            return recursive_load(rango_fechas, data.vstack(df))
        except Exception as e:
            print(f"Error al procesar {archivo}: {str(e)}")
            return recursive_load(rango_fechas, data)

    # Directorio base
    fder = folder.parent / 'All_Data'

    # Inicializar rango de fechas
    year_i, month_i = date_i.split('-')
    year_f, month_f = date_f.split('-')
    rango_fechas = crea_rango(year_i, month_i, year_f, month_f)

    # Cargar datos
    start_time = time.time()
    data = recursive_load(rango_fechas, pl.DataFrame())
    elapsed_time = time.time() - start_time

    # Informar tiempo de finalización
    print(f"Extracción completada en {elapsed_time:.2f} segundos.")
    return data

def limpiar_nombre_cliente(nombre: str) -> str:
    # Reemplazos específicos de nombres completos
    reemplazos_especificos = {
        "DES INM PADRE WERNER HOTEL HAMPTON": "HAMPTON LAS CONDES",
        "ADMINISTRADORA_TURISMO_ROSA_AGUSTINA": "ROSA AGUSTINA_GUANAQUEROS",
        "ROSA_AGUSTINA": "ROSA AGUSTINA_OLMUE",
        "MINERA LOS PELAMBRES": "MINERA LOS PELAMBRES",
        "CIA. NAC. DE CUEROS S.A": "CUEROS NACIONALES",
        "IMERYS_CHILE_SPA": "IMERYS",
        "ESTABLECIMIENTO PENITENCIARIO CCP": "GENDARMERIA DE CHILE REGIONAL ANTOFAGASTA"
    }

    # Limpieza de nombres irrelevantes
    patrones_excluir = r"\b(S\.?A\.?|SPA|LIMITADA|LTDA|CCP|EX|COMUNICACIONES|S\.A\.?|EIRL|S A)\b"
    
    # Revisar si el nombre tiene un reemplazo específico
    if nombre in reemplazos_especificos:
        return reemplazos_especificos[nombre]

    # Limpiar palabras irrelevantes
    nombre_limpio = re.sub(patrones_excluir, '', nombre, flags=re.IGNORECASE)

    # Remover múltiples espacios y capitalizar adecuadamente
    return ' '.join(nombre_limpio.strip().split()).upper()


def get_ivt_cliente(folder: Path, cliente: str, barra: str, date_i: str, date_f: str):
    def recursive_load(rango_fechas, data):
        try:
            fecha = next(rango_fechas)
            #print(f"Procesando fecha: {fecha}")
        except StopIteration:
            print("Fin del rango de fechas.")
            return data

        year, month = fecha.split('-')

        if year > date_f.split('-')[0] or (year == date_f.split('-')[0] and int(month) > int(date_f.split('-')[1])):
            print(f"Fecha {fecha} fuera del rango permitido.")
            return data

        fder = folder.parent / 'All_Data'
        archivo = f"IVT_{year[-2:]}_{month}.parquet"

        try:
            df = pl.read_parquet(fder / archivo)
        except Exception as e:
            print(f"Error obteniendo datos de {archivo}: {e}")
            return data

        cliente_regex = '|'.join([x.strip().upper() for x in cliente.split(',')])
        barra_regex = barra.upper()

        df = df.filter(
            pl.col('Cliente').str.to_uppercase().str.contains(cliente_regex) & 
            pl.col('nombre_barra').str.to_uppercase().str.contains(barra_regex)
        )

        # **Asignar el nombre convertido al cliente filtrado**
        nombre_convertido = limpiar_nombre_cliente(cliente)
        df = df.with_columns(pl.lit(nombre_convertido).alias('Cliente'))

        return recursive_load(rango_fechas, data.vstack(df))

    start_time = time.time()

    year_i, month_i = date_i.split('-')
    year_f, month_f = date_f.split('-')

    rango_fechas = crea_rango(year_i, month_i, year_f, month_f)

    data = recursive_load(rango_fechas, pl.DataFrame())

    data = data.rename({'nombre_barra': 'Barra'})

    elapsed_time = time.time() - start_time
    print(f'Extracción de IVT cliente completada en {elapsed_time:.2f} segundos.')

    return data

# Busca clientes y barras específicos asociados en los datos de CMg para una fecha específica.
#
# :param sh: Objeto de hoja de cálculo para mostrar mensajes de progreso.
# :param folder: Carpeta base donde se encuentran los archivos de datos.
# :param barra: Barras específicas para buscar (separadas por comas).
# :param date_i: Fecha inicial 
# :param date_f: Fecha final en formato "YYYY-MM".
# :return: Lista ordenada de barras encontradas.
def busca_cliente_bdd(folder: Path, cliente: str, date_i: str, date_f: str):
    year_i, month_i = date_i.split('-')
    year_f, month_f = date_f.split('-')
    
    # Ruta del archivo parquet
    fder = folder.parent / 'All_Data'
    df = pl.read_parquet(fder / f"IVT_{year_f[-2:]}_{month_f}.parquet")  # Consolidado de los .parquet de las fechas de entrada

    cliente = '|'.join([x.strip().upper() for x in cliente.split(',')])  # Adaptar la entrada
    con = duckdb.connect()  # Conectar base de datos duckdb
    con.register("clientes", df)  # Registrar df en la base de datos en una tabla "clientes"
    
    # Consulta SQL
    query = f"SELECT DISTINCT Cliente, nombre_barra FROM clientes WHERE Cliente LIKE '%{cliente}%'"
    results = con.execute(query).df()
    
    # Validación de columnas
    if "Cliente" not in results.columns or "nombre_barra" not in results.columns:
        raise ValueError("La consulta no devolvió las columnas esperadas: 'Cliente' y 'nombre_barra'")
    
    # Rellenar valores faltantes
    results.fillna({"Cliente": "Desconocido", "nombre_barra": "Sin Barra"}, inplace=True)
    
    # Crear lista de resultados
    results_list = []
    for _, row in results.iterrows():
        cliente = row.get("Cliente", "Desconocido")
        barra = row.get("nombre_barra", "Sin Barra")
        results_list.append({"Cliente": cliente, "Barra": barra})
    
    return results_list


class DataIVT:
    cols_data_IVT = [
        "nombre_barra",
        "propietario",
        "Tipo_Medida",
        "clave",
        "descripcion",
    ]

    def __init__(self, path, año, mes):
        self.path = path
        self.año = año
        self.mes = str(mes).zfill(2)

    def hour_col_to_date_col_new(self, cols_ivt=True):
        df_fin = self.df_data.copy()

        if cols_ivt:
            cols_ivt = self.cols_data_IVT + ["Valores"]
            inicio_int = 6
        else:
            cols_ivt = [self.cols_data_IVT[0]]
            inicio_int = 1

        fecha = [int(x) for x in df_fin.columns[inicio_int:]]
        start_date = pd.to_datetime(f"20{self.año}-{int(self.mes)}-01")
        horas = pd.to_timedelta([x - 1 for x in fecha], unit="h")
        horas += start_date

        df_fin.columns = cols_ivt + horas.to_list()
        return df_fin


class ClientesIVT(DataIVT):
    def __init__(self, path_ivt, año, mes):
        super().__init__(path_ivt, año, mes)
        self.ivt = self.path / f"IVT_{self.año}_{self.mes}.parquet"
        self.df_data = pd.read_parquet(self.ivt)

    def busca_cliente(self, cliente):
        df = self.hour_col_to_date_col_new()
        cols = self.cols_data_IVT
        if isinstance(cliente, str):
            df = df.loc[df["descripcion"].str.upper().str.contains(cliente)]
        elif isinstance(cliente, list):
            df = df.loc[df["descripcion"].str.upper().str.contains("|".join(cliente))]

        df["nombre"] = (
            df["nombre_barra"]
            + ";"
            + df["propietario"]
            + ";"
            + df["Tipo_Medida"]
            + ";"
            + df["clave"]
            + ";"
            + df["descripcion"]
        )

        df.drop(columns=cols, inplace=True)
        df.drop(columns="Valores", inplace=True)
        df.set_index("nombre", inplace=True)
        return df.T

def busca_barra_cmg(folder: Path, barras: str, date_i: str, date_f: str):
    """
    Busca múltiples barras en un rango de archivos Parquet y devuelve una lista de coincidencias.

    Args:
        folder (Path): Ruta base que contiene los datos.
        barras (str): Barras separadas por comas (e.g., "ARICA, POLPAICO, ANTOFA").
        date_i (str): Fecha inicial en formato 'YYYY-MM'.
        date_f (str): Fecha final en formato 'YYYY-MM'.

    Returns:
        list: Lista de barras encontradas.
    """
    # Extraer rango de fechas usando crea_rango
    agno_i, mes_i = date_i.split('-')
    agno_f, mes_f = date_f.split('-')
    rango_fechas = crea_rango(agno_i, mes_i, agno_f, mes_f)

    fder = folder.parent / 'All_Data'

    # Preparar las barras para la búsqueda
    barras = [barra.strip().upper() for barra in barras.split(',')]

    # Crear una conexión con DuckDB
    con = duckdb.connect()

    # Acumular resultados en un conjunto
    all_results = set()

    for fecha in rango_fechas:
        year, month = fecha.split('-')
        parquet_file = fder / f"CMg_{year[-2:]}_{month}_def.parquet"

        # Verificar si el archivo Parquet existe
        if not parquet_file.exists():
            print(f"Archivo no encontrado: {parquet_file}")
            continue

        # Cargar el archivo Parquet
        df = pl.read_parquet(parquet_file)

        # Registrar la tabla en DuckDB
        con.register("barras", df)

        # Buscar cada barra en el archivo actual
        for barra in barras:
            query = f"SELECT DISTINCT Barra FROM barras WHERE Barra LIKE '%{barra}%'"
            results = con.execute(query).df()
            all_results.update(results["Barra"].tolist())

    return list(all_results)



def buscarClientesPorBarra(folder: Path, barra_seleccionada: str, date_i: str, date_f: str, print=None):
    """
    Busca clientes asociados a una barra específica y reporta progresivamente.
    Args:
        folder (Path): Ruta base que contiene los datos.
        barra_seleccionada (str): Nombre de la barra seleccionada.
        date_i (str): Fecha inicial en formato 'YYYY-MM'.
        date_f (str): Fecha final en formato 'YYYY-MM'.
        print (func, optional): Función para reportar resultados incrementales.
    """
    try:
        # Extraer año y mes de las fechas de inicio y fin
        year_i, month_i = date_i.split('-')
        year_f, month_f = date_f.split('-')

        # Lista para almacenar los clientes únicos encontrados
        clientes_unicos = set()

        # Iterar sobre el rango de fechas
        for fecha in crea_rango(year_i, month_i, year_f, month_f):
            fder = folder.parent / 'All_Data'
            archivo_parquet = fder / f"IVT_{fecha.split('-')[0][2:]}_{fecha.split('-')[1]}.parquet"
            try:
                # Leer archivo Parquet
                df = pl.read_parquet(archivo_parquet)

                # Filtrar por barra seleccionada
                barra_seleccionada_normalizada = barra_seleccionada.strip().upper()
                df = df.with_columns(
                    df["nombre_barra"].str.strip_chars().str.to_uppercase().alias("nombre_barra_normalizado")
                )
                df_filtrado = df.filter(df["nombre_barra_normalizado"] == barra_seleccionada_normalizada)

                # Extraer clientes únicos y notificar
                for cliente in df_filtrado["Cliente"].unique().to_list():
                    if cliente not in clientes_unicos:
                        clientes_unicos.add(cliente)
                        if print:
                            print(f"Cliente encontrado: {cliente}")
                        yield cliente  # Reportar cliente encontrado

            except Exception as e:
                if print:
                    print(f"Error al procesar {archivo_parquet}: {str(e)}")

        # Validar resultados
        if not clientes_unicos:
            if print:
                print("No se encontraron clientes para la barra seleccionada.")
            yield "No se encontraron clientes para la barra seleccionada."

    except Exception as e:
        if print:
            print(f"Error al buscar clientes: {str(e)}")
        yield f"Error al buscar clientes: {str(e)}"
