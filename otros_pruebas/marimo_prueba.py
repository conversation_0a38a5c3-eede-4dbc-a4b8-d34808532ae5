"""
Empezar con:
    marimo edit "nombre archivo"
    marimo edir marimo_prueba
"""

import marimo

__generated_with = "0.12.2"
app = marimo.App(width="medium")


@app.cell
def _():
    import marimo as mo
    import pandas as pd
    import polars as pl
    from pathlib import Path
    return Path, mo, pd, pl


@app.cell
def _(Path):
    data_folder = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\Plataforma Gestión CGECx\All_Data')
    files = list(data_folder.glob('IVT*24*.parquet'))
    print(f'encontrado: {len(files)} archivos')
    return data_folder, files


@app.cell
def _(files, pl):
    df_fin = pl.DataFrame()
    cliente = 'AITUE'
    for i, file in enumerate(files):
        print(f'procesando archivo {i} de {len(files)}')
        df = pl.read_parquet(file)
        df = df.filter(pl.col('Cliente').str.to_uppercase().str.contains(cliente.upper()))
        df_fin = pl.concat([df_fin, df])
    return cliente, df, df_fin, file, i


@app.cell
def _(df_fin):
    df2 = df_fin.pivot(index='Fecha', values='Consumo [kWh]', columns='propietario', aggregate_fn='sum')
    return (df2,)


@app.cell
def _():
    return


if __name__ == "__main__":
    app.run()
