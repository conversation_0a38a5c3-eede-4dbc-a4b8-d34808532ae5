"""
Consolidación de Archivos Excel

Este script abre múltiples archivos Excel, concatena su información y guarda
el resultado en un archivo llamado "consolidado.xlsx".

Para los archivos después del primero, se omiten los encabezados (primera fila)
ya que todos los archivos tienen la misma estructura.

Autor: CGE
Fecha: 2024-04
"""

import polars as pl
import pandas as pd
import os
import glob
from pathlib import Path
import sys

def convert_column_types(df):
    """
    Convierte explícitamente los tipos de datos para las columnas problemáticas.

    Args:
        df (pl.DataFrame): DataFrame a convertir

    Returns:
        pl.DataFrame: DataFrame con tipos de datos convertidos
    """
    # Lista de índices de columnas problemáticas
    problematic_columns = [13, 14, 15, 21, 23, 25, 27, 28, 30, 31, 33, 37]

    # Verificar que el DataFrame tiene suficientes columnas
    if df.shape[1] <= max(problematic_columns):
        print(f"Advertencia: El DataFrame tiene {df.shape[1]} columnas, pero se esperaban al menos {max(problematic_columns) + 1}.")
        return df

    # Procesar cada columna problemática
    for col_idx in problematic_columns:
        if col_idx < df.shape[1]:
            col_name = df.columns[col_idx]
            col_dtype = df.dtypes[col_idx]

            # Si la columna es de tipo string, primero limpiar los valores
            if str(col_dtype) == "String":
                try:
                    # Primero reemplazar strings vacíos con None y luego reemplazar comas
                    df = df.with_columns([
                        pl.when(pl.col(col_name) == "").then(pl.lit(None))
                          .otherwise(pl.col(col_name).str.replace(",", ""))
                          .alias(col_name)
                    ])

                    # Ahora convertir a Float64
                    df = df.with_columns([
                        pl.col(col_name).cast(pl.Float64)
                    ])
                    print(f"Columna {col_idx} ({col_name}) convertida de String a Float64 con manejo de strings vacíos y comas")
                except Exception as e:
                    print(f"Error al convertir columna {col_idx} ({col_name}): {e}")
            else:
                try:
                    # Para otros tipos, intentar convertir directamente
                    df = df.with_columns([
                        pl.col(col_name).cast(pl.Float64)
                    ])
                    print(f"Columna {col_idx} ({col_name}) convertida a Float64")
                except Exception as e:
                    print(f"Error al convertir columna {col_idx} ({col_name}): {e}")

    return df

def consolidar_archivos_excel(directorio=None, patron=None,
                              output_path="_consolidado.xlsx"):
    """
    Consolida múltiples archivos Excel en uno solo.

    Args:
        directorio (str): Directorio donde se encuentran los archivos Excel
        patron (str): Patrón para buscar archivos específicos (ej: "*.xlsx")
        output_path (str): Ruta donde se guardará el archivo consolidado

    Returns:
        pl.DataFrame: DataFrame consolidado
    """
    # Obtener la lista de archivos Excel
    if patron:
        file_paths = glob.glob(patron)
    elif directorio:
        file_paths = glob.glob(f"{directorio}\\*.xlsx") + glob.glob(f"{directorio}\\*.xls")
    else:
        file_paths = []

    if not file_paths:
        print("No se encontraron archivos Excel para procesar.")
        return None

    print(f"Se encontraron {len(file_paths)} archivos Excel:")
    for i, path in enumerate(file_paths):
        print(f"{i+1}. {path}")

    # Inicializar DataFrame consolidado
    consolidated_df = None

    # Procesar cada archivo
    for i, file_path in enumerate(file_paths):
        print(f"\nProcesando archivo {i+1}/{len(file_paths)}: {file_path}")

        try:
            # Verificar si el archivo existe
            if not os.path.exists(file_path):
                print(f"Error: El archivo {file_path} no existe. Saltando...")
                continue

            # Determinar el tipo de archivo por su extensión
            file_ext = Path(file_path).suffix.lower()

            if file_ext not in ['.xlsx', '.xls']:
                print(f"Error: El archivo {file_path} no es un archivo Excel. Saltando...")
                continue

            # Leer el archivo
            if i == 0:
                # Para el primer archivo, leer con encabezados
                df = pl.read_excel(file_path)
                print(f"Leído archivo con {df.shape[0]} filas y {df.shape[1]} columnas")

                # Convertir explícitamente los tipos de datos para las columnas problemáticas
                df = convert_column_types(df)

                # Almacenar los datos consolidados
                consolidated_df = df
            else:
                # Para los archivos siguientes, omitir la primera fila (encabezados)
                df = pl.read_excel(file_path)
                # Omitir la primera fila (encabezados)
                df = df.slice(1, df.shape[0] - 1)
                print(f"Leído archivo con {df.shape[0]} filas y {df.shape[1]} columnas")

                # Convertir explícitamente los tipos de datos para las columnas problemáticas
                df = convert_column_types(df)

                # Concatenar con los datos consolidados
                if consolidated_df is not None:
                    consolidated_df = pl.concat([consolidated_df, df])
                else:
                    consolidated_df = df

            print(f"Total acumulado: {consolidated_df.shape[0]} filas")

        except Exception as e:
            print(f"Error al procesar el archivo {file_path}: {e}")
            import traceback
            traceback.print_exc()

    # Guardar los datos consolidados
    if consolidated_df is not None and consolidated_df.shape[0] > 0:
        print(f"\nGuardando datos consolidados en {output_path}...")

        try:
            # Convertir a pandas para exportar a Excel
            pandas_df = consolidated_df.to_pandas()
            pandas_df.to_excel(output_path, index=False)
            print(f"Archivo Excel guardado en: {output_path}")
        except Exception as e:
            print(f"Error al guardar el archivo consolidado: {e}")
            # Intentar guardar como CSV como alternativa
            try:
                csv_path = Path(output_path).with_suffix('.csv')
                consolidated_df.write_csv(csv_path)
                print(f"Archivo CSV guardado en: {csv_path}")
            except Exception as e2:
                print(f"Error al guardar como CSV: {e2}")
    else:
        print("No hay datos para guardar.")

    return consolidated_df

def main():
    """Función principal del script."""
    print("=== CONSOLIDACIÓN DE ARCHIVOS EXCEL ===")

    # Verificar argumentos de línea de comandos
    if len(sys.argv) > 1:
        # Si se proporciona un directorio o patrón como argumento
        arg = sys.argv[1]

        if os.path.isdir(arg):
            # Es un directorio
            print(f"Procesando archivos Excel en el directorio: {arg}")
            consolidar_archivos_excel(directorio=arg)
        elif '*' in arg:
            # Es un patrón
            print(f"Procesando archivos Excel que coinciden con el patrón: {arg}")
            consolidar_archivos_excel(patron=arg)
        else:
            print(f"Error: {arg} no es un directorio válido ni un patrón de archivos.")
            print("Uso: python consolidar_excel.py [directorio|patrón]")
    else:
        # Solicitar directorio al usuario
        directorio = input("Ingrese la ruta del directorio con los archivos Excel: ")

        if os.path.isdir(directorio):
            consolidar_archivos_excel(directorio=directorio)
        else:
            print(f"Error: {directorio} no es un directorio válido.")

if __name__ == "__main__":
    main()
