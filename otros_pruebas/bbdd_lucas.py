import pandas as pd
from pathlib import Path
import re
#%%
folder = Path(r'C:\Users\<USER>\Desktop\Vario CGE\Nueva carpeta')
file = 'Búsqueda al 28-04-2025.xlsx'

df = pd.read_excel(folder / file, sheet_name='Reporte Proyectos Filtrados')
#%%
df = df.loc[df['Sector Económico'].str.upper().str.contains('INMOBILIAR')]

df[columna_dividir] = df[columna_dividir].astype(str).str.lower()
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('.', '')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace(',', '.')

df[columna_dividir] = df[columna_dividir].astype(str).str.replace('departamentos.', 'deptos')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('departamentos', 'deptos')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('departamento', 'deptos')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('deptos.', 'deptos')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('casas.', 'casas')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('viviendas/310.', 'viviendas')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('viviendas/310', 'viviendas')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('viviendas.', 'viviendas')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('locales', 'local')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('edificaciones', 'edificios')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('m2.', 'm2')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('m2;', 'm2')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('/', '')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('(', '')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace(')', '')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('hás', 'ha')
df[columna_dividir] = df[columna_dividir].astype(str).str.replace('unidades.', 'unidades')
#%%
nombre_columna = 'Capacidad de Producción'

df_resultado = df.copy()
df_resultado[f'{nombre_columna}_split'] = df_resultado[nombre_columna].astype(str).str.split()

# Crear listas para almacenar los pares de valores
pares_valores = []
for idx, row in df_resultado.iterrows():
    valores_split = row[f'{nombre_columna}_split']
    pares_fila = []

    # Recorrer los valores divididos y crear pares (numérico, string)
    i = 0
    while i < len(valores_split) - 1:
        # Verificar si el valor actual es numérico
        if re.match(r'^[\d.,]+$', valores_split[i]):
            # Si es numérico, formar un par con el siguiente valor (string)
            pares_fila.append((valores_split[i], valores_split[i + 1]))
            i += 2  # Avanzar dos posiciones
        else:
            # Si no es numérico, avanzar una posición
            i += 1

    pares_valores.append(pares_fila)
df_resultado[f'{nombre_columna}_pares'] = pares_valores

# Crear columnas adicionales para cada par encontrado
max_pares = max([len(pares) for pares in pares_valores])
for i in range(max_pares):
    # Extraer el valor numérico del par
    df_resultado[f'{nombre_columna}_valor_num_{i + 1}'] = df_resultado[
        f'{nombre_columna}_pares'].apply(
        lambda x: x[i][0] if i < len(x) else None)

    # Extraer el valor string del par
    df_resultado[f'{nombre_columna}_valor_str_{i + 1}'] = df_resultado[
        f'{nombre_columna}_pares'].apply(
        lambda x: x[i][1] if i < len(x) else None)

df_resultado = df_resultado.drop([f'{nombre_columna}_split', f'{nombre_columna}_pares'], axis=1)
df_resultado.to_excel(folder / 'out.xlsx', index=False)
#%%
