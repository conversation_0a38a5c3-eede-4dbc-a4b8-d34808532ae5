import pandas as pd
import polars as pl
import modin.pandas as mpd
from pathlib import Path
from app import lectura
import time

#%%
"""
17-01-2025
Se hizo una reliquidación de Enero a Junio 2022:
- Calcular el efecto en cada cliente.
"""

medidas_r = {
    '01': '01_Ene/Medidas_horarias_2201_R02D.xlsb',
    '02': '02_Feb/Medidas_horarias_2202_R02D.xlsb',
    '03': '03_Mar/Medidas_horarias_2203_R02D.xlsb',
    '04': '04_Abr/Medidas_horarias_2204_R04D.xlsb',
    '05': '05_May/Medidas_horarias_2205_R02D.xlsb',
    '06': '06_Jun/Medidas_horarias_2206_R02D.xlsb'
}
medidas = {
    '01': '01_Ene/def/Medidas_horarias_2201.xlsb',
    '02': '02_Feb/def/Medidas_horarias_2202.xlsb',
    '03': '03_Mar/def/Medidas_horarias_2203.xlsb',
    '04': '04_Abr/def/Medidas_horarias_2204.xlsb',
    '05': '05_May/def/Medidas_horarias_2205.xlsb',
    '06': '06_Jun/def/Medidas_horarias_2206.xlsb'
}

def lee_medidas(file, hoja='Hoja2'):
    df_pl = lectura.read_xlsb_to_polars(file, hoja)
    df = pd.DataFrame(df_pl[4:], columns=df_pl[3])
    cgec = df.loc[df['propietario'] == 'CGE_C']
    cgec.to_excel(path / f'cgec_{mes}.xlsx', index=False)
    return cgec

#%%
# leer archivos de medidas_r, y extraer consumo y CMg de nuestros clientes
ti = time.time()
mes = '06'
path = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\2025\01_Reliquidación2022')
archivo = path / medidas[mes]

lee_medidas(archivo)

print(f'Tiempo lectura Medidas: {round(time.time() - ti, 4)}')
#%%
