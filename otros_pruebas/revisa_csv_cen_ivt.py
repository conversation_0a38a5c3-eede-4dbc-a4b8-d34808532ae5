import polars as pl
from pathlib import Path


#%%
folder = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\General CGE Cx\Facturación PPAs\Cálculo Facturación\2025\202503\08. IVT-Pre')
file = 'Balance_Valorizado_2503_Data_VALORIZADO_15min.csv'

df = pl.read_csv(folder / file, separator=',', ignore_errors=True)
#%%
df.columns
#%%
agro = df.filter(
    pl.col('clave').str.to_uppercase().str.contains('AGROMAITGASS')
)
#%%
agro.write_csv(folder / 'agro.csv')