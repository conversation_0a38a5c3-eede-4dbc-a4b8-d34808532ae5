#%%
import polars as pl
import pandas as pd
from pathlib import Path
import os
import re

from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from difflib import get_close_matches

from bbdd_cmg import get_cmg_barra, get_ivt_cliente
from collections import defaultdict

from workalendar.america import Chile


def agrupar_clientes_similares(clientes_barras):
    cliente_barras_dict = defaultdict(list)
    nombres_limpios = []
    for cliente_barra in clientes_barras:
        if "(Barra:" in cliente_barra:
            cliente, barra = cliente_barra.split("(Barra: ", maxsplit=1)
            barra = barra.rstrip(")")
        else:
            cliente, barra = cliente_barra, "DESCONOCIDO"

        cliente_limpio = cliente.strip().upper().replace("S.A.", "").replace("SPA", "").strip()
        # Verificar en excepciones
        for nombre_limpio, variantes in excepciones.items():
            if cliente_limpio in [v.upper() for v in variantes]:
                cliente_limpio = nombre_limpio
                break
        match = get_close_matches(cliente_limpio, nombres_limpios, n=1, cutoff=0.8)
        if match:
            cliente_limpio = match[0]
        cliente_barras_dict[cliente_limpio].append(cliente_barra)
        if cliente_limpio not in nombres_limpios:
            nombres_limpios.append(cliente_limpio)
    return cliente_barras_dict

def limpiar_nombre(nombre):
    nombre = nombre.encode('latin1').decode('utf-8', 'ignore') if isinstance(nombre, str) else str(nombre)
    nombre_limpio = re.sub(r'[<>:"/\\|?*]', '_', nombre)
    nombre_limpio = nombre_limpio.encode('utf-8', 'ignore').decode('utf-8')
    return nombre_limpio.strip()

def buscar_archivos_dinamico(ruta_carpeta):
    """Busca recursivamente archivos Excel en subcarpetas tipo 2024, 2025, etc."""
    archivos_validos = []
    for year_folder in ruta_carpeta.iterdir():
        if year_folder.is_dir() and re.match(r'^\d{4}$', year_folder.name):
            for archivo in year_folder.glob("*.xlsx"):
                archivos_validos.append(archivo)
    print(f"Total archivos válidos encontrados: {len(archivos_validos)} en {ruta_carpeta}")
    return archivos_validos

def limpiar_nombre_facturacion(nombre):
    nombre = nombre.strip()
    if nombre in reemplazos_especificos:
        return reemplazos_especificos[nombre]
    return nombre

def buscar_datos(ruta_archivo, nombre_cliente):
    cliente_limpio = limpiar_nombre_facturacion(nombre_cliente)
    try:
        df = pd.read_excel(ruta_archivo, dtype=str)
    except Exception as e:
        print(f"Error al leer el archivo {ruta_archivo}: {e}")
        return []
    """
    print("Nombres de los clientes en el archivo:")
    for nombre in df['NOMBRE']:
        print(nombre)"""
    resultado = df[df['NOMBRE'].str.contains(cliente_limpio, case=False, na=False)]
    if not resultado.empty:
        registros = []
        for _, row in resultado.iterrows():
            registros.append({
                "clave": row['INSTAL Cx'],
                "folio": row['Folio'],
                "url": row['URL']
            })
        return registros
    else:
        print(f"No se encontraron registros para el cliente: {cliente_limpio}")
        return []

def filtrar_por_folio(nombre_cliente, ruta_archivo_2, folio, url, clave):

    try:
        df = pd.read_excel(ruta_archivo_2, dtype=str, sheet_name='Datos fact')
        columnas_necesarias = [
            "Instalación", "Folio", "Glosa Operacion Parcial", "Importe Neto Actual",
            "Fecha Vencimiento", "I. P. Serv. Act."
        ]
        columnas_similares = [col for col in df.columns if 'folio' in col.lower()]
        if columnas_similares:
            columna_folio = columnas_similares[0]
            df_filtrado = df[df[columna_folio] == folio]
            if df_filtrado.empty:
                return pd.DataFrame()
            columnas_disponibles = [col for col in columnas_necesarias if col in df_filtrado.columns]
            df_filtrado = df_filtrado[columnas_disponibles]
            df_filtrado["URL"] = url
            df_filtrado["Cliente"] = nombre_cliente
            df_filtrado["Clave"] = clave
            if "Fecha Vencimiento" in df_filtrado.columns:
                df_filtrado["Fecha Vencimiento"] = pd.to_datetime(
                    df_filtrado["Fecha Vencimiento"],
                    dayfirst=True,
                    errors='coerce').dt.date
                #print("La columna 'Fecha Vencimiento' ha sido convertida a formato AAAA-MM-DD.")
            #print("Se agregó la columna URL con éxito.")
            return df_filtrado
        else:
            #print("No se encontró una columna similar a 'Folio'.")
            return pd.DataFrame()
    except FileNotFoundError:
        print(f"Error: El archivo {ruta_archivo_2} no se encontró.")
    except ValueError as e:
        print(f"Error al leer el archivo: {e}")
    except Exception as e:
        print(f"Error inesperado: {e}")
    return pd.DataFrame()

def procesar_archivos_por_folio(nombre_cliente, archivos_estatus, folio, url, clave):
    df_consolidado = pd.DataFrame()

    for archivo in archivos_estatus:
        df_filtrado = filtrar_por_folio(nombre_cliente, archivo, folio, url, clave)
        if not df_filtrado.empty:
            df_consolidado = pd.concat([df_consolidado, df_filtrado], ignore_index=True)
    return df_consolidado

def es_dia_habil(fecha):
    return cal.is_working_day(fecha)

def mostrar_consumo(
    allData,
    lista_resultados,
    entrada_ruta,
    entrada_fecha_inicio,
    entrada_fecha_fin,
    tipo_exportacion,
):
    try:
        if not lista_resultados:
            print("La lista de resultados está vacía. Proporciona un cliente válido.")
            return

        clientes_barras = {}
        for resultado in lista_resultados:
            cliente_parts = resultado.split(' (Barra: ')
            if len(cliente_parts) < 2:
                continue
            cliente_name = limpiar_nombre_facturacion(cliente_parts[0].strip())
            barra_name = cliente_parts[1].replace(')', '').strip()
            clientes_barras.setdefault(cliente_name, []).append(barra_name)

        ruta = Path(entrada_ruta)


        all_combined_data = pl.DataFrame()
        for cliente, barras in clientes_barras.items():
            print(f'Procesando consumos y costos marginales...')
            for barra in barras:
                #print(f"Procesando Cliente: {cliente}, Barra: {barra}")
                consumo_data = get_ivt_cliente(folder=allData, cliente=cliente, barra=barra,
                                               date_i=entrada_fecha_inicio, date_f=entrada_fecha_fin)
                cmg_data = get_cmg_barra(folder=allData, barra=barra, date_i=entrada_fecha_inicio, date_f=entrada_fecha_fin)
                if consumo_data.shape[0] == 0 or cmg_data.shape[0] == 0:
                    print(f"No se encontraron datos para Cliente: {cliente}, Barra: {barra}")
                    continue
                combined_data = consumo_data.join(cmg_data, on="Fecha", how="inner")
                combined_data = combined_data.with_columns(
                    pl.col("Fecha").map_elements(lambda fecha: es_dia_habil(fecha.date())).alias("EsDiaHabil")
                )
                all_combined_data = pl.concat([all_combined_data, combined_data], how="vertical")
        if all_combined_data.shape[0] == 0:
            print("No se encontraron datos para ningún cliente o barra.")
            return
        if tipo_exportacion == "excel":
            excel_path = ruta / 'Consumo_CMG.xlsx'
            all_combined_data.write_excel(excel_path)
        elif tipo_exportacion == "powerbi":
            parquet_path = ruta / 'Consumo_CMG.parquet'
            all_combined_data.write_parquet(parquet_path)
            print(f"Archivo Parquet guardado en {parquet_path}.")
    except Exception as e:
        print(f"Error durante la obtención de consumo y costos marginales: {e}")

def obtener_Info_Contratos(clave, ruta_cliente):
    clave = int(clave)
    ruta_contratos = Path(r"C:\Users\<USER>\Desktop\Reporte Clientes\Contratos.xlsx")
    df_contratos = pd.read_excel(ruta_contratos, sheet_name='BBDD clientes actuales', engine='openpyxl')
    df_contratos['INSTAL Cx'] = df_contratos['INSTAL Cx'].fillna(0)
    df_contratos['INSTAL Cx'] = df_contratos['INSTAL Cx'].astype(int)
    df_filtrado = df_contratos[df_contratos['INSTAL Cx'] == clave]
    if not df_filtrado.empty and len(df_filtrado) == 1:
        archivo_salida_contrato = ruta_cliente / "contrato_info.xlsx"
        df_filtrado.to_excel(archivo_salida_contrato, index=False, engine='openpyxl')
        print(f"Archivo de contrato guardado exitosamente en: {archivo_salida_contrato}")
    else:
        print(f"No se encontró un cliente único para la clave '{clave}'.")

def crear_carpetas_y_procesar(clientes_barras, ruta_documentos, ruta_estatus, ruta_allData):
    fecha_inicio = "2024-01"
    fecha_fin = "2025-04"

    script_dir = Path(__file__).parent
    base_ruta = script_dir
    clientes_agrupados = agrupar_clientes_similares(clientes_barras)
    for cliente_limpio, barras_cliente in clientes_agrupados.items():
        ruta_cliente = base_ruta / limpiar_nombre(cliente_limpio)
        os.makedirs(ruta_cliente, exist_ok=True)
        print(f"Procesando cliente consolidado: {cliente_limpio}")
        archivos_validos = buscar_archivos_dinamico(ruta_documentos)
        resultados = []
        for archivo in archivos_validos:
            registros = buscar_datos(archivo, cliente_limpio)
            if registros:
                for registro in registros:
                    url = registro["url"]
                    folio = registro["folio"]
                    clave = registro["clave"]
                    resultados.append((url, folio, clave))
                    #print(f"\nResultado Final: Folio: {folio} | URL: {url} en el archivo {archivo.name}")
        if not resultados:
            print(f"No se encontraron datos para el cliente '{cliente_limpio}' en los archivos válidos.")
        df_consolidado_cliente = pd.DataFrame()
        archivos_estatus = buscar_archivos_dinamico(ruta_estatus)
    print(f'Abriendo archivos de facturación: {cliente_limpio}, {len(archivos_estatus)}')
    for url, folio, clave in resultados:
        df_filtrado = procesar_archivos_por_folio(cliente_limpio, archivos_estatus, folio, url, clave)
        df_consolidado_cliente = pd.concat([df_consolidado_cliente, df_filtrado], ignore_index=True)

    mostrar_consumo(
        ruta_allData,
        lista_resultados=barras_cliente,
        entrada_ruta=str(ruta_cliente),
        entrada_fecha_inicio=fecha_inicio,
        entrada_fecha_fin=fecha_fin,
        tipo_exportacion="powerbi",
    )
    if not df_consolidado_cliente.empty:
        archivo_salida_cliente = ruta_cliente / f"Facturación.xlsx"
        df_consolidado_cliente.to_excel(archivo_salida_cliente, index=False, engine='openpyxl')
        print(f"Archivo guardado exitosamente en: {archivo_salida_cliente}")
    else:
        print(f"No se generaron resultados para el cliente '{cliente_limpio}'.")
    clave_obtenida = None
    for _, _, clave in resultados:
        if clave_obtenida is None:
            obtener_Info_Contratos(clave, ruta_cliente)
            clave_obtenida = clave
