import polars as pl
from pathlib import Path

#%% Homologa IVT
folder = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\CGE_Automatizacion\All_Data')
ivt = 'IVT_25_02.parquet'

ivt = pl.read_parquet(Path(folder) / ivt)
columnas_ivt = ivt.columns

#%%
ivt = ivt.filter(pl.col('Agno') >= 2025)
#%%
ivt.write_parquet(folder / 'CMg_2025.parquet')
#%%
fold = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\Plataforma Gestión CGECx\Facturacion\2025\202504\_CMg')
ar = 'CMg_25_03_def.parquet'
df = pl.read_parquet(fold / ar)
#%%
folder = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\Plataforma Gestión CGECx\Facturacion\2025\202506\_CMg')
cmg = 'CMg_25_05_def.parquet'
df = pl.read_parquet(folder / cmg)
#%%
qui = df.filter(pl.col('Barra').str.contains('QUIANI________013'))
#%%
qui.to_pandas().to_excel(folder / 'qui.xlsx', index=False)
#%%
