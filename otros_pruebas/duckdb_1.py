import duckdb
from pathlib import Path

#%%
data_folder = Path(
    r'C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\Plataforma Gestión CGECx\All_Data'
)
con = duckdb.connect(database='cmg_ivt.db', read_only=False)
#%%
def carga_data(tipo='CMg'):
    archivos = list(str(x) for x in data_folder.glob(f'{tipo}*.parquet'))
    if tipo == 'CMg':
        tabla = 'CMg'
    elif tipo == 'IVT':
        tabla = 'IVT'
    else:
        print('tipo debe ser CMg o IVT')
        return

    con.execute(f"""
            CREATE OR REPLACE TABLE {tipo} AS 
            SELECT * FROM read_parquet({archivos})
        """)
    # Agregar índices para acelerar consultas
    if tipo == 'CMg':
        con.execute("CREATE INDEX IF NOT EXISTS idx_fecha ON CMg(Fecha)")
        con.execute("CREATE INDEX IF NOT EXISTS idx_barra ON CMg(Barra)")
    else:
        con.execute("CREATE INDEX IF NOT EXISTS idx_fecha ON IVT(Fecha)")
        con.execute("CREATE INDEX IF NOT EXISTS idx_nombre_barra ON IVT(nombre_barra)")
        con.execute("CREATE INDEX IF NOT EXISTS idx_clave ON IVT(clave)")
        con.execute("CREATE INDEX IF NOT EXISTS idx_Cliente ON IVT(Cliente)")
    con.commit()
    con.close()

#%%
def promedio_diario_mes(año, mes, barra=None):
    query = """
    SELECT 
        CAST(Fecha AS DATE) AS dia,
        AVG("CMg [USD/MWh]") AS CMg_promedio,
        COUNT(*) AS registros
    FROM CMg
    WHERE 
        date_part('year', Fecha) = ? AND 
        date_part('month', Fecha) = ?
    """
    params = [año, mes]

    if barra:
        query += " AND Barra = ?"
        params.append(barra)

    query += " GROUP BY dia ORDER BY dia"

    return conn.execute(query, params).fetchdf()

def promedios_horarios_mes(año, mes, barra=None):
    try:
        query = """
            SELECT 
                EXTRACT(HOUR FROM Fecha) AS hora,
                AVG("CMg [USD/MWh]") AS precio_promedio,
                COUNT(*) AS registros,
            FROM CMg
            WHERE 
                EXTRACT(YEAR FROM Fecha) = ? AND 
                EXTRACT(MONTH FROM Fecha) = ?
            """
        params = [año, mes]

        if barra:
            query += ' AND Barra = ?'
            params.append(barra)

        query += """
            GROUP BY hora
            ORDER BY hora
            """

        result = conn.execute(query, params).fetchdf()

        if result.empty:
            print(f"No hay datos para {mes}/{año}" + (
                f" en {barra}" if barra else ""))
            return None

        # Formatear resultados
        result['hora'] = result['hora'].astype(int)
        result = result.rename(
            columns={"precio_promedio": "CMg Promedio [USD/MWh]"})

        return result

    except Exception as e:
        print(f"Error al calcular promedios horarios: {str(e)}")
        return None

#%%
def buscar_barra(patron, limite=20):
    """
    Busca barra que contengan el patrón especificado en cualquier parte de su
    nombre

    Args:
        patron (str): Texto a buscar (ej. 'lu')
        limite (int): Máximo de resultados a devolver

    Returns:
        DataFrame: Con las columnas
            [Barra, registros, primera_fecha, ultima_fecha]
                  ordenado por cantidad de registros (descendente)
    """
    try:
        if not patron or not isinstance(patron, str):
            print("Error: El patrón de búsqueda debe ser un texto no vacío")
            return None

        query = """
        SELECT 
            "Barra",
            COUNT(*) AS registros,
            MIN(Fecha) AS primera_fecha,
            MAX(Fecha) AS ultima_fecha
        FROM CMg
        WHERE LOWER("Barra") LIKE LOWER(?)
        GROUP BY "Barra"
        ORDER BY registros DESC
        LIMIT ?
        """

        # Agregamos % al inicio y final para buscar en cualquier posición
        parametro_busqueda = f"%{patron}%"

        result = conn.execute(query, [parametro_busqueda, limite]).fetchdf()

        if result.empty:
            print(f"No se encontraron Barras que contengan '{patron}'")
            return None

        return result

    except Exception as e:
        print(f"Error en la búsqueda: {str(e)}")
        return None
#%%
carga_data('CMg')
carga_data('IVT')

#%%
barra = 'L.VILOS_______220'
#barra = 'C.NAVIA_______110'
with duckdb.connect(database='cmg_ivt.db', read_only=False) as conn:
    df_diario = promedio_diario_mes(2025, 2, barra)
    df_horario = promedios_horarios_mes(2025, 2, barra)
#%%
with duckdb.connect(database='cmg_ivt.db', read_only=False) as conn:
    bar = buscar_barra('vilos', 100)
print(bar)
#%%
with duckdb.connect(database='cmg_ivt.db', read_only=False) as conn:
    ivt = conn.sql("SELECT * FROM IVT")
    df = ivt.limit(10).to_df()
    print(df.columns)
    #ivt = ivt.to_df()

#%%
ivt