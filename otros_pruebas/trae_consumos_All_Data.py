import polars as pl
from pathlib import Path
from datetime import datetime
from dateutil.relativedelta import relativedelta

def define_fecha_ivt(fecha):
    if fecha.day < 22:
        nueva_fecha = fecha - relativedelta(months=2)
    else:
        nueva_fecha = fecha - relativedelta(months=1)
    return nueva_fecha

def fecha_inicio(fecha, rango=12):
    rang1 = fecha - relativedelta(months=rango)
    return rang1

def crea_rango(agno_i, mes_i, agno_f, mes_f):
    #rango = []
    #f_aux = f'{agno_i}-{mes_i}'
    a_aux = agno_i
    mes_aux = mes_i
    while f'{a_aux}-{mes_aux}' != f'{agno_f}-{mes_f}':
        yield f'{a_aux}-{str(mes_aux).zfill(2)}'
        if int(mes_aux) == 12:
            a_aux = str(int(a_aux) + 1)
            mes_aux = 1
        else:
            a_aux = a_aux
            mes_aux = str(int(mes_aux) + 1)
    yield f'{agno_f}-{str(mes_aux).zfill(2)}'

def get_ivt_cliente(folder: Path, cliente: str, date_hoy: datetime, meses=12):
    """
    :param sh: hoja del excel donde se trabaja
    :param folder: carpeta donde se encuentran los archivos
    :param cliente: Cliente o clientes que se desea buscar
    :param date_i: fecha inicial
    :param date_f: fecha final
    :return:
    """
    ff = define_fecha_ivt(date_hoy)
    fi = fecha_inicio(ff, meses)

    year_i, month_i = str(fi.year), str(fi.month).zfill(2)
    year_f, month_f = str(ff.year), str(ff.month).zfill(2)

    rango_fechas = crea_rango(year_i, month_i, year_f, month_f)
    fder = folder / 'All_Data'

    data = pl.DataFrame()
    fecha = next(rango_fechas)
    while fecha is not None:
        year = fecha.split('-')[0]
        month = fecha.split('-')[1]
        archivo_parquet = fder / f"IVT_{year[-2:]}_{month}.parquet"
        if archivo_parquet.exists():
            print(f'Obteniendo datos de {fecha}')
            df = pl.read_parquet(archivo_parquet)
            cliente = '|'.join([x.strip().upper() for x in cliente.split(',')])
            df = df.filter(pl.col('Cliente').str.to_uppercase().str.contains(cliente))
            data = data.vstack(df)
            fecha = next(rango_fechas, None)
        else:
            print(f'Proceso finalizado')
            fecha = next(rango_fechas, None)
            data = data.rename({'nombre_barra': 'Barra'})
            return data

    return data

def busca_cliente_bdd(folder: Path, cliente: str, date: str):
    year_f, month_f = date.split('-')

    fder = folder / 'All_Data'
    data = pl.read_parquet(fder / f"IVT_{year_f[-2:]}_{month_f}.parquet")
    cliente = '|'.join([x.strip().upper() for x in cliente.split(',')])

    data = data.filter(pl.col('Cliente').str.to_uppercase().str.contains(cliente))
    data = data.with_columns(
        [(pl.col('Cliente') + ', ' + pl.col('nombre_barra')).alias('cl')]
    )

    data = data.select(pl.col(['cl']).unique()).to_series()
    return data.sort()

#%%
folder = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\CGE_Automatizacion')
folder_out = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\2025\Licitaciones\fout')
hoy = datetime.now()
fi = define_fecha_ivt(hoy)
fi = f'{fi.year}-{str(fi.month).zfill(2)}'
#%%
clientes = ['CASINO DEL LAGO', 'kuden', 'maderera san']
cl = busca_cliente_bdd(folder, ','.join(clientes), fi)
print(cl)
#%%
df = get_ivt_cliente(folder, ','.join(clientes), hoy)
df = df.with_columns([
    (pl.col("clave") + "_" + pl.col("Cliente")).alias("clave_cliente")])
#%%
df_gr = df.pivot(
    values='Consumo [kWh]',
    index='Fecha',
    on='clave_cliente',
    aggregate_function='first')
#%%
archivo = f'consumos_{str(hoy.year)}-{str(hoy.month).zfill(2)}'
df_gr.write_excel(
    folder_out / archivo,
    autofit=True,
    worksheet=archivo
)
#%%