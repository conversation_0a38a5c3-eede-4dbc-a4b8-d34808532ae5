import subprocess
from pathlib import Path


def crear_requerimientos_y_comprimir(directorio_proyectos, directorio_destino,
                                     ruta_7zip):
    """
    Recorre las subcarpetas, crea el requirements.txt y comprime el proyecto,
    excluyendo el ambiente virtual, carpetas 'build' y 'dist', y archivos .parquet y .pbix,
    usando 7-Zip.
    """
    proyectos_path = Path(directorio_proyectos)
    destino_path = Path(directorio_destino)

    destino_path.mkdir(parents=True, exist_ok=True)

    extensiones_a_excluir = ['.parquet', '.pbix', '.csv', '.tsv', '.zip', '.7z']

    for proyecto_path in proyectos_path.iterdir():
        if not proyecto_path.is_dir():
            continue

        print(f"Procesando proyecto: {proyecto_path.name}")

        # 1. Encontrar el ambiente virtual y crear el requirements.txt
        venv_path = None
        for sub_path in proyecto_path.iterdir():
            if sub_path.is_dir() and 'Scripts' in [p.name for p in
                                                   sub_path.iterdir()]:
                venv_path = sub_path
                break

        if venv_path:
            print(f"  > Ambiente virtual encontrado en: {venv_path}")
            requirements_file = proyecto_path / "requirements.txt"

            comando_requerimientos = f'call "{venv_path / "Scripts" / "activate"}" && pip freeze > "{requirements_file}"'

            try:
                subprocess.run(comando_requerimientos, shell=True, check=True)
                print(f"  > requirements.txt creado en {requirements_file}")
            except subprocess.CalledProcessError as e:
                print(
                    f"  > Error al crear requirements.txt en {proyecto_path.name}: {e}")
                continue

        # 2. Comprimir la carpeta, excluyendo elementos no deseados
        nombre_archivo_7z = f"{proyecto_path.name}_backup.7z"
        ruta_archivo_7z = destino_path / nombre_archivo_7z

        comando_7z = [str(ruta_7zip), 'a', '-t7z', '-mx=9',
            # Nivel de compresión máxima
            str(ruta_archivo_7z), str(proyecto_path), ]

        # Añadir exclusiones al comando
        if venv_path:
            comando_7z.append(f'-xr!{venv_path.name}')

        # Excluir las carpetas 'build' y 'dist'
        for folder_name in ['build', 'dist']:
            if (proyecto_path / folder_name).is_dir():
                comando_7z.append(f'-xr!{folder_name}')
                print(
                    f"  > Carpeta '{folder_name}' encontrada y marcada para exclusión.")

        for ext in extensiones_a_excluir:
            comando_7z.append(f'-xr!*{ext}')

        print(f"  > Comprimiendo {proyecto_path.name}...")
        try:
            subprocess.run(comando_7z, check=True, capture_output=True,
                           text=True)
            print(f"  > Backup creado con éxito: {nombre_archivo_7z}")
            print("-" * 40)
        except subprocess.CalledProcessError as e:
            print(
                f"  > Error al crear el archivo 7z de {proyecto_path.name}: {e.stderr}")
            print("-" * 40)


# --- Configuración y uso del script ---
# ⚠️ ¡IMPORTANTE! ⚠️
# 1. Cambia estas rutas a las de tu sistema.
# 2. La ruta a 7z.exe debe ser la correcta.
# 3. El comando para activar el venv es para Windows; si usas Linux o macOS, debe ser ajustado.
RUTA_CARPETA_PROYECTOS = Path(r"C:\Python apps\_FCA_scripts")
RUTA_CARPETA_DESTINO = Path(r"C:\py_backup")
RUTA_7ZIP_EXE = Path(r"C:\Program Files\7-Zip\7z.exe")

crear_requerimientos_y_comprimir(directorio_proyectos=RUTA_CARPETA_PROYECTOS,
    directorio_destino=RUTA_CARPETA_DESTINO, ruta_7zip=RUTA_7ZIP_EXE)