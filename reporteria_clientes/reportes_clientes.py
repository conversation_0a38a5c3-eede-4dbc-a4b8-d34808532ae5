import polars as pl
import pandas as pd
import numpy as np
from pathlib import Path

from otros_pruebas import bbdd_cmg as bd

from workalendar.america import Chile

cal = Chile()

folder_gral = Path(r"C:\Users\<USER>\OneDrive - Grupo CGE")
all_data = folder_gral / r"BBDD\CGE_Automatizacion\All_Data"
estatus = folder_gral / r"General CGE Cx\Reportes Leo Facturas\04. Estatus Libres  CGE Cx"
documentos = folder_gral / (r"General CGE Cx\Reportes Leo Facturas\03. Reporte documentos emitidos")
folder_contratos = Path(r'C:\Users\<USER>\Desktop\Reporte Clientes')

map_clientes= {
    'ADMINISTRADORA_TURISMO_ROSA_AGUSTINA': [
        'ADMINISTRADORA_TURISMO_ROSA_AGUSTINA',
        'ROSA_AGUSTINA'],
    'LAS ACACIAS S A': [
        'LAS ACACIAS S A',
        'FT FOODS S.A. (LAS ACACIAS S.A .)'],
    'HOTEL HAMPTON': [
        'HOTEL HAMPTON',
        'DES INM PADRE WERNER HOTEL HAMPTON',
        'SANTIAGO AIRPORT HOTEL'],
    'MINERA_POTRERILLOS _LA_CRUZ DIEGO_DE_ALMAGRO_023': [
        'MINERA_POTRERILLOS _LA_CRUZ DIEGO_DE_ALMAGRO_023',
        'MINERA_POTRERILLOS_ _LA_CRUZ DIEGO_DE_ALMAGRO_023',
        'Minera Potrerillos'],
    'AGRORESERVAS DE CHILE SPA': ['AGRORESERVAS DE CHILE SPA',
        'AGRORESERVAS DE CHILE'],
    'INMOB VEGA MONUMENTAL S.A.': ['INMOB VEGA MONUMENTAL S.A.',
        'COMERCIALIZADORA VEGA MONUMENTAL'],
    'EXPORTADORA DE FRUTAS DEL SUR SA': [
        'EXPORTADORA DE FRUTAS DEL SUR SA',
        'EXPORTADORA_DE_FRUTAS_DEL_SUR_SA'],

    'CLARO CHILE S A': ['CLARO CHILE S A'],
    'VTR COMUNICACIONES SPA': ['VTR COMUNICACIONES SPA'],
    'COCA COLA DE CHILE S A' : ['COCA COLA DE CHILE S A'],
    'MARIA LORETO FERNANDEZ LEON': ['MARIA LORETO FERNANDEZ LEON'],
    'FORESTAL AITUE LTDA': ['FORESTAL AITUE LTDA'],
    'IMERYS_CHILE_SPA': ['IMERYS_CHILE_SPA'],
    'EX ABASOLO VALLEJOS': ['EX ABASOLO VALLEJOS'],
    'SOC COMERCIAL E INDUSTRIAL PLAY CAR': ['SOC COMERCIAL E INDUSTRIAL PLAY CAR'],
    'MINERA_LOS_PELAMBRES': ['MINERA_LOS_PELAMBRES'],
    'MAGASA': ['MAGASA'],
    'CIA. NAC. DE CUEROS S.A': ['CIA. NAC. DE CUEROS S.A'],
    'AGROINDUSTRIA SAN FRANCISCO LTDA': ['AGROINDUSTRIA SAN FRANCISCO LTDA'],
    'ESTABLECIMIENTO PENITENCIARIO CCP': ['ESTABLECIMIENTO PENITENCIARIO CCP'],
    'SOC AGRICOLA EL OLIBAL': ['SOC AGRICOLA EL OLIBAL'],
    'UNIVERSIDAD DE LA FRONTERA': ['UNIVERSIDAD DE LA FRONTERA'],
    'SCHÜSSLER': ['SCHÃ\x9cSSLER', 'SCHÜSSLER'],
    'AGROINDUSTRIAL ANAPROC S.A.': ['AGROINDUSTRIAL ANAPROC S.A.'],
    'CAMARICO SPA': ['CAMARICO SPA'],
    'AGROSUPER GAS SUR': ['AGROSUPER GAS SUR']
}

folder_out = Path(r'C:\Users\<USER>\Desktop\Reporte Clientes')

fecha_i = "2024-01"
fecha_f = "2025-04"
propietario = "CGE_C"

#%%
reemplazos_especificos = {
    "DES INM PADRE WERNER HOTEL HAMPTON": "OPERACIONES HOTELERAS MT SPA",
    "AGROSUPER GAS SUR": "AGROCOMERCIAL AS",
    "COCA COLA DE CHILE S A": "COCA-COLA",
    "SOC AGRICOLA EL OLIBAL": "SOC.AGRICOLA EL OLIBAL",
    "HOTEL HAMPTON": "OPERACIONES HOTELERAS MT SPA",
    "IMERYS_CHILE_SPA": "IMERYS MINERALES CHILE LTDA",
    "MINERA_POTRERILLOS _LA_CRUZ DIEGO_DE_ALMAGRO_023": "POTRERILLOS",
    "CIA. NAC. DE CUEROS S.A": "CUEROS",
    "CLARO CHILE S A": "CLARO",
    "INMOB VEGA MONUMENTAL S.A.": "INMOB VEGA MONUMENTAL S A",
    "AGROINDUSTRIAL ANAPROC S.A.": "AGROINDUSTRIAL ANAPROC S.A.",
    "EX ABASOLO VALLEJOS": "AVSA INVERSIONES LTDA",
    "CAMARICO SPA": "EXPORTADORA CAMARICO SA",
    "SCHÃSSLER": "SCHUSSLER",
    "ESTABLECIMIENTO PENITENCIARIO CCP": "GENDARMERIA",
    "FORESTAL AITUE LTDA": "AITUE",
    "AGROINDUSTRIA SAN FRANCISCO LTDA": "AGROINDUSTRIA SAN FRANCISCO",
    "MINERA_LOS_PELAMBRES": "PELAMBRES",
    "ROSA_AGUSTINA": "AGUSTINA LTDA",
    "ADMINISTRADORA_TURISMO_ROSA_AGUSTINA": "AGUSTINA LIMITADA",
    "COMERCIALIZADORA VEGA MONUMENTAL": "INMOB VEGA MONUMENTAL S A",
    "EXPORTADORA_DE_FRUTAS_DEL_SUR_SA": "EXPORTADORA DE FRUTAS DEL SUR SA",
    "FT FOODS S.A. (LAS ACACIAS S.A .)": "LAS ACACIAS S A",
}

excepciones = {
    "ROSA_AGUSTINA": ["ROSA_AGUSTINA", "ADMINISTRADORA_TURISMO_ROSA_AGUSTINA"],
    "HOTELES HAMPTON - SANTIAGO AIRPORT": ["SANTIAGO AIRPORT HOTEL", "DES INM PADRE WERNER HOTEL HAMPTON", "HOTEL HAMPTON"],
    "SCHUSSLER": ["SCHÃSSLER", "SCHUSSLER"],
    "EXPORTADORA DE FRUTAS DEL SUR SA": ["EXPORTADORA DE FRUTAS DEL SUR SA", "EXPORTADORA_DE_FRUTAS_DEL_SUR_SA"],
    "LAS ACACIAS S A": ["LAS ACACIAS S A", "FT FOODS S.A. (LAS ACACIAS S.A .)"],
    "COCA COLA DE CHILE S A": ["COCA COLA DE CHILE S A", "COCA-COLA"],
    # ... agrega más si lo necesitas ...
}

clientes_barras = [
    "UNIVERSIDAD DE LA FRONTERA (Barra: LASENCINAS____015)",
    "MAGASA (Barra: PLASCASAS_____013)",
    "ESTABLECIMIENTO PENITENCIARIO CCP (Barra: ANTOFAGASTA___013)",
    "AGROINDUSTRIA SAN FRANCISCO LTDA (Barra: STA.ELVIRA____013)",
    "CIA. NAC. DE CUEROS S.A (Barra: PIDUCO________013)",
]

#%%
def extrae_clientes_propietario(data, propietario, rango_fechas):
    df_fin = pl.DataFrame()
    for i, f in enumerate(rango_fechas):
        print(f'Procesando fecha : {f}', end='\r')
        df = pl.read_parquet(data / f'IVT_{f[2:4]}_{f[-2:]}.parquet')
        df = df.filter(pl.col('propietario') == propietario)
        df_fin = df_fin.vstack(df)
    return df_fin

def extrae_cmg_rango(data, rango_fechas):
    df_fin = pl.DataFrame()
    for i, f in enumerate(rango_fechas):
        print(f'Procesando fecha : {f}', end='\r')
        df = pl.read_parquet(data / f'CMg_{f[2:4]}_{f[-2:]}_def.parquet')
        df_fin = df_fin.vstack(df)
    return df_fin

def es_dia_habil(fecha):
    return cal.is_working_day(fecha)

def procesa_CMg_por_cliente(folder_salida, dic_clientes, data):
    folder_salida.mkdir(parents=True, exist_ok=True)
    print("\nIniciando procesamiento de clientes...")
    for nombre_carpeta_destino, clientes_a_filtrar in dic_clientes.items():
        # Construir la ruta completa de la carpeta para este grupo de clientes
        ruta_carpeta_actual = folder_salida / nombre_carpeta_destino
        ruta_carpeta_actual.mkdir(parents=True, exist_ok=True)

        # Filtrar el DataFrame para los clientes de este grupo
        df_filtrado = data.filter(pl.col("Cliente").is_in(clientes_a_filtrar))

        if not df_filtrado.is_empty():
            # Definir el nombre del archivo de salida (puedes ajustar esto)
            nombre_archivo = 'Consumo_CMG.parquet'
            df_filtrado.write_parquet(ruta_carpeta_actual / nombre_archivo)
        else:
            print(
                f"  - Sin Data: {clientes_a_filtrar}. Carpeta creada  sin "
                f"archivo.")
    print("\nProceso completado.")

#%% Extrae demanda de CGE_C
rango = bd.crea_rango(fecha_i[:4], fecha_i[5:], fecha_f[:4], fecha_f[5:])
df_cgec = extrae_clientes_propietario(all_data, propietario, rango)

rango = bd.crea_rango(fecha_i[:4], fecha_i[5:], fecha_f[:4], fecha_f[5:])
df_cmg = extrae_cmg_rango(all_data, rango)

df_cgec = df_cgec.rename({'nombre_barra': 'Barra'})
df_gr = df_cgec.join(df_cmg, on=['Fecha', 'Barra'], how='left')

df_gr = df_gr.drop_nulls()
df_gr = df_gr.with_columns(pl.col('Barra').alias('Barra_right'))

df_gr = df_gr.with_columns(
    pl.col("Fecha").map_elements(
        es_dia_habil, return_dtype=pl.Boolean).alias("EsDiaHabil")
)

orden_columnas = [
    'Fecha', 'Barra', 'propietario', 'Tipo_Medida', 'clave', 'Cliente',
    'Consumo [kWh]', 'Barra_right', 'CMg [USD/MWh]', 'USD', 'EsDiaHabil']
df_gr = df_gr.select(orden_columnas)
#%% para saber cómo se llaman los clientes en los archivos del IVT
clientes = df_gr['Cliente'].unique().to_list()
print(clientes)

#%% Procesa a clientes y deja el parquet de CMg en carpeta respectiva

procesa_CMg_por_cliente(folder_out, map_clientes, df_gr)

#%%
f_2 = Path(r'C:\Users\<USER>\Desktop\Reporte Clientes\old\AGROINDUSTRIAL ANAPROC')
df_exp = pl.read_parquet(f_2 / 'Consumo_CMG.parquet')

#%% Procesa archivos de facturación emitidos del "Leo"
print(estatus)
print(documentos)

#%%
df_cl = pd.read_excel(folder_contratos / 'Contratos.xlsx', sheet_name='BBDD clientes actuales')

#%%
def procesa_estatus(folder_estatus):
    posibles_formatos_entrada = [
        "%d-%m-%Y %H:%M:%S",  # dd-mm-yyyy hh:mm:ss
        "%d-%m-%Y",  # dd-mm-yyyy
        "%d.%m.%Y",  # dd.mm.yyyy
        "%d/%m/%Y %H:%M",  # dd/mm/yyyy hh:mm
        "%d/%m/%Y",  # dd/mm/yyyy
    ]

    df_estatus = pd.DataFrame()
    for archivo in folder_estatus.rglob("*"):
        if archivo.is_file():
            print(f'Procesando archivo: {archivo.name}')
            df = pd.read_excel(archivo, sheet_name='Datos fact')
            columnas_necesarias = [
                "Instalación", "Folio", "Glosa Operacion Parcial",
                "Importe Neto Actual", "Fecha Vencimiento", "I. P. Serv. Act."]
            df = df[columnas_necesarias]
            for col in ["Fecha Vencimiento", "I. P. Serv. Act."]:
                df[col] = pd.to_datetime(
                    df[col],
                    format='mixed',
                    errors='coerce',
                    dayfirst=True)
                df[col] = df[col].dt.strftime("%d.%m.%Y")
            df_estatus = pd.concat([df_estatus, df])
    return df_estatus

def procesa_documentos(folder_documentos):
    df_docs = pd.DataFrame()
    for archivo in folder_documentos.rglob("*"):
        if archivo.is_file():
            print(f'Procesando archivo: {archivo.name}')
            df = pd.read_excel(archivo)
            df_docs = pd.concat([df_docs, df])
    df_docs = df_docs.dropna(subset=['URL'])
    return df_docs


#%%
df_estatus = procesa_estatus(estatus)
df_docs = procesa_documentos(documentos)
#%%
df_docs.to_excel(folder_out / 'docs.xlsx', index=False)
df_estatus.to_excel(folder_out / 'estatus.xlsx', index=False)

#%%
map_fact= {
    'AGRORESERVAS DE CHILE SPA': ['AGRORESERVAS DE CHILE SPA '],
    'AGROINDUSTRIAL ANAPROC S.A.': ['AGROINDUSTRIAL ANAPROC S.A. '],
    'COCA COLA DE CHILE S A' : ['COCA-COLA DE CHILE S.A. '],
    'FORESTAL AITUE LTDA': ['FORESTAL AITUE LTDA. '],
    'INMOB VEGA MONUMENTAL S.A.': ['INMOB VEGA MONUMENTAL S A '],
    'MINERA_LOS_PELAMBRES': ['MINERA LOS PELAMBRES '],
    'HOTEL HAMPTON': ['OPERACIONES HOTELERAS MT SPA ',
                      'SANTIAGO AIRPORT HOTEL SPA '],
    'SOC AGRICOLA EL OLIBAL': ['SOC.AGRICOLA EL OLIBAL '],
    'UNIVERSIDAD DE LA FRONTERA': ['UNIVERSIDAD DE LA FRONTERA '],
    'EX ABASOLO VALLEJOS': ['AVSA INVERSIONES LTDA '],
    'SCHÜSSLER': ['SCHUSSLER SA. '],
    'ADMINISTRADORA_TURISMO_ROSA_AGUSTINA': ['ADMINISTRADORA DE TURISMO ROSA AGUSTINA LTDA'],
    'ESTABLECIMIENTO PENITENCIARIO CCP': ['GENDARMERIA DE CHILE DIRECCION REGIONAL ANTOFAGASTA'],
    'MINERA_POTRERILLOS _LA_CRUZ DIEGO_DE_ALMAGRO_023': ['COMPAÑÍA MINERA POTRERILLOS LIMITADA'],
    'CIA. NAC. DE CUEROS S.A': ['CIA. NAC. DE CUEROS S.A'],
    'IMERYS_CHILE_SPA': ['IMERYS MINERALES CHILE LTDA.'],
    'CAMARICO SPA': ['EXPORTADORA CAMARICO SA '],
    'MAGASA': ['FORESTAL MAGASA LTDA.'],
    'VTR COMUNICACIONES SPA': ['VTR COMUNICACIONES SPA - La cisterna',
                               'VTR COMUNICACIONES SPA - Peñalolen',
                               'VTR COMUNICACIONES SPA- Independencia',
                               'VTR COMUNICACIONES SPA- La Florida'],
    'CLARO CHILE S A': ['CLARO CHILE SPA'],
    'LAS ACACIAS S A': ['FT FOODS S.A'],
    'EXPORTADORA DE FRUTAS DEL SUR SA': ['EXPORTADORA DE FRUTAS DEL SUR S.A'],
    'AGROINDUSTRIA SAN FRANCISCO LTDA': ['AGROINDUSTRIA SAN FRANCISCO LTDA.'],
    'AGROSUPER GAS SUR': ['AGROCOMERCIAL AS LIMITADA']
}

#%%

def procesa_facturacion(folder_salida, dic_clientes, data, docs, contratos):
    folder_salida.mkdir(parents=True, exist_ok=True)
    print("\nIniciando procesamiento de facturacion...")

    for nombre_carpeta_destino, clientes_a_filtrar in dic_clientes.items():
        # Construir la ruta completa de la carpeta para este grupo de clientes
        ruta_carpeta_actual = folder_salida / nombre_carpeta_destino
        ruta_carpeta_actual.mkdir(parents=True, exist_ok=True)
        # crea el archivo contratos para cada cliente
        df_contrato = contratos.loc[contratos['NOMBRE'].isin(clientes_a_filtrar)]

        n_cli = contratos.loc[contratos['NOMBRE'].isin(
            clientes_a_filtrar), 'INSTAL Cx'].to_list()
        n_cli = [int(x) for x in n_cli if
                 not (isinstance(x, float) and np.isnan(x))]

        # Filtrar el DataFrame para los clientes de este grupo
        df = data.loc[data['Instalación'].isin(n_cli)]
        df_d = docs.loc[docs['INSTAL Cx'].isin(n_cli)]

        df_d.rename(columns={'INSTAL Cx': 'Instalación'}, inplace=True)

        df_d2 = df_d.copy()
        df = df.merge(df_d2, on=['Instalación', 'Folio'], how='left')
        cols = ['Instalación', 'Folio', 'Glosa Operacion Parcial',
                'Importe Neto Actual', 'Fecha Vencimiento', 'I. P. Serv. Act.',
                'URL']
        df = df[cols]
        df['Cliente'] = nombre_carpeta_destino
        df['Clave'] = df['Instalación']

        if not df.empty:
            # Definir el nombre del archivo de salida (puedes ajustar esto)
            nombre_archivo = 'Facturación.xlsx'
            n2 = 'contrato_info.xlsx'
            df.to_excel(ruta_carpeta_actual / nombre_archivo, index=False)
            df_contrato.to_excel(ruta_carpeta_actual / n2, index=False)
        else:
            print(
                f"  - Sin Data: {clientes_a_filtrar}. Carpeta creada  sin "
                f"archivo.")
    print("\nProceso completado.")

#%%
procesa_facturacion(folder_out, map_fact, df_estatus, df_docs, df_cl)
