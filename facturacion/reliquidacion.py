import polars as pl
import app.write_to_excel as we
from pathlib import Path
from datetime import datetime

def prepara_cmg(path_file, cout):
    df = pl.read_csv(path_file, ignore_errors=True, separator=';')
    df = df.drop(['nombre_barra', 'tension', 'INTERVALO', 'Cuarto de Hora'])
    df.write_parquet(cout / 'CMg_15min.parquet')
    return df

def junta_medidas_cmg(path_medidas, path_cmg, cout, suf_name):
    dfm = pl.read_parquet(path_medidas)
    dfc = pl.read_parquet(path_cmg)
    dfm = dfm.filter(pl.col("Suministrador").str.contains("CGE_C"))

    dfm = pasa_horas(dfm)
    dfc = pasa_horas(dfc)
    df = dfm.join(dfc, on=['fecha', 'hora', 'minuto', 'Barra'], how='left')

    #df.write_excel(cout / 'medias_cmg.xlsx')
    we.write_xlsx_values(df, cout / f'medias_cmg_{suf_name}.xlsx')
    return df

def pasa_horas(df, claveagnomes="Clave_Anio_Mes", qhora='Cuarto de Hora'):
    if 'FECHA' in df.columns:
        df = df.rename({'FECHA': 'fecha', 'HORA': 'hora', 'MINUTO': 'minuto',
                        'nombre_barra_cmg': 'Barra'})

        df = df.with_columns(
            pl.col("fecha").cast(pl.Utf8).str.strptime(
                pl.Date, "%Y%m%d", strict=False))
        df = df.with_columns((pl.col('hora') - 1))

    else:
        df = df.rename({'nombre_barra_cmg': 'Barra'})
        agnomes = str(df.select(pl.first(claveagnomes)).item())
        f_inicial = datetime(int("20" + agnomes[:2]), int(agnomes[-2:]), 1)

        df = df.with_columns([(pl.lit(f_inicial) + (
                    pl.col(qhora).cast(pl.Int64) - 1) * pl.duration(
            minutes=15)).alias("cuarto_dt"), ])
        df = df.with_columns([pl.col("cuarto_dt").dt.date().alias("fecha"),
            pl.col("cuarto_dt").dt.hour().alias("hora"),
            pl.col("cuarto_dt").dt.minute().alias("minuto"), ]).with_columns(
            [pl.col("cuarto_dt").alias(qhora)]).drop(
            ["cuarto_dt", claveagnomes, qhora])
    return df

#%%
carpeta = Path(r'C:\Users\<USER>\Desktop\Rev Facturación\07_Julio')
file_cmg = 'cmg2507_15min_formateado def.csv'
sufix = carpeta.name

pth_out= carpeta / 'Procesados'
pth_out.mkdir(exist_ok=True)

file_resumen = pth_out / '_resumen.xlsx'
barras = pl.read_excel(file_resumen)
barras = list(barras.select(pl.col("Barra")))[0]

#%%
cmg = prepara_cmg(carpeta / file_cmg, pth_out)
#%%
sufix = carpeta.name
df = junta_medidas_cmg(pth_out / 'Medidas_15min.parquet',
                       pth_out / 'CMg_15min.parquet',
                       pth_out,
                       sufix)
#%%
carpeta.name
