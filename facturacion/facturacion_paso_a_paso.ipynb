#%%
from apps.paso_04 import analisis_consumos
from apps.paso_05 import get_file_from_zip, lee_cmg_csv_def_to_parquet
from apps.paso_06 import descarga_cmg_15min
from apps.paso_07 import descomprime_zip, procesa_csv_cmg15min
from apps.paso_08 import corre_main
from apps.paso_09 import proceso_sscc
from pathlib import Path
import pandas as pd
import glob
#%% md
# Paso 1

En la carpeta de Facturación, se crea la carpeta "añomes", ejemplo 202401, o 202412.
Dentro se crea la carpeta "01. Envío Anto", y se procede a dejar los siguientes archivos dentro.

1. <PERSON> principio del proceso, <PERSON>, o el área de facturación hace llegar el archivo de facturación.
   * El formato es: Formato carga precios CGE Cx año-mes.xlsx

2. <PERSON><PERSON>, este archivo se le saca todos los "links", y se borran todas  hojas, dejando sólo la hoja "Precios", se marca en amarillo las columnas "PCtX", "CSP" y " PPOT nudo troncal". Además se agregan 2 columnas al final, en una se indica a que sistema de transmisión corresponde la barra del cliente, y en la otra, el voltaje de la barra de retiro.

3. Al archivo se le cambia el nombre, y se le agrega al final "_small".
   * Quedando así: Formato carga precios CGE Cx año-mes_small.xlsx

4. Este archivo es enviado a María Antonieta, la que nos enrtegará el archivo de facturación mas "_entrega" con los precios pedidos.
#%% md
# Paso 2

En paralelo, o después del "Paso 1", hay que pedir las medidas de consumo de cada uno de nuestros clientes:
   * CGE: a Fernando Madrid - Rony Tapia
   * Chilquinta: Christian Albornoz - <EMAIL>
   * ENEL: William Assen, <EMAIL>
   * Transelec: a Renzo Castro, <EMAIL>

Se crea la carpeta "02. Consumos", y se procede a dejar los archivos entregados dentro.
#%% md
# Paso 3

Se crea la carpeta "03 Cálculo de Precios", y se procede a hacer el cálculo de los precios para facturar.
Hay que tener en cuenta que cada suministrador entrega la data en distinto formato.

Una vez entregada la información de consumo, crear un archivo con TODA la información de consumos hacia abajo.
Queda para el futuro crear un script que una la información, y que quede homologada.

Al pasar a la columna Consumo, hay que dividir TODOS los valores de consumo por 4 (para aquellos cuya medición sea kWd). Esto se hace para CGE, Chilquinta y Transelec. Enel entrega sus datos en unidades de energía.

   * Archivo de CGE: Se entrega el id_medidor, y hay que hacer el cruce con el id_punto_medida. Copiar la columna de id_punto_medida, fecha_hora y KWD (al final KWD debe ser promediado).
   * Chilquinta: Copiar la fecha y el consumo. Tener cuidado en que la fecha sea bien reconocida. Luego agregar a mano el punto de medida correspondiente (al momento de este documento es sólo Rosa Agustinas Olmué).
   * ENEL: Lo entrega en formato CSV, ojo con los formatos de separación de columnas, y en los números decimales. El valor de NombrePunto, FECHA, HORA y kWhd. El NombrePunto es el id_punto_medida, el consumo es kWhd, el cual hay que sumarlo, al ser una medida de energía, y se debe junta la fecha y la hora, para que quede en el formato adecuado.
   * Transelec: Se entrega el consumo de AGROMAITGASS (a la fecha). Luego copiar el consumo kWd, y la fecha y hora, hay que llevarlo al formato adecuado.
#%% md
# Paso 4
En la carpeta Raiz del mes de facturación, se debe copiar el archivo "Data_cli.xlsx", que es el archivo que se debe ir actualizando, y que tiene la información para realizar el cruce entre el punto de medida y el nombre - barras  y fee's asociados al contrato.

Se comienza a procesar la demanda:

#%%
# Procesa Consumos. El archivo final será un parquet en la carpeta de consumos con el siguiente nombre, yy_mm_pre.parquet

#Carpeta_Facturacion = r'C:\Users\<USER>\Desktop\Vario CGE\_Facturacion\202411'
Carpeta_Facturacion = r'C:\Users\<USER>\Desktop\Vario CGE\_Facturacion\202412'
#CarpetaConsumos = '02_consumos'
CarpetaConsumos = '_CMg'
archivo_data_cli =  "Data_cli.xlsx"
archivo_consumos_totales = 'ConsumosTotales.xlsx'

año_mes = Carpeta_Facturacion[-4:]
data = analisis_consumos(
    path_consumos=Path(Carpeta_Facturacion) / CarpetaConsumos,
    nom_inf_clientes=archivo_data_cli,
    nom_consumos=archivo_consumos_totales,
    nom_final=f'{año_mes[:2]}-{str(int(año_mes[2:])-1)}_pre.parquet'
)
print(f'Archivo parquet de consumos creado en la carpeta de "{CarpetaConsumos}"')
#%% md
# Paso 5
Se comienza a descargar los costos marginales definitivos y preliminares.
Intentar descargar los costos marginales con el script de más abajo, si no se puede, hay que hacerlo a mano.
  * Definitivos: Se descargan los Definitivos del mes N-2, y todos los que se tengan del mes N-1
    * Los definitivos del mes N-2, deben estar ya al salir los definitivos de ese mes, y deben estar en la carpeta "09. IVT-Def"
    en este archivo "03-Bases-de-Datos_yymm_BD01-2.zip", hay que poenr esta referencia para que el script lo detecte
  * Preliminares: Son los de los mes N-1 que no hayan salido ddefinitivos.

estos se descargan de:
 https://www.coordinador.cl/mercados/documentos/transferencias-economicas/costo-marginal-real/


#%%
# Costos Marginales Definitivos N-2. ojo que hay que direccionarlo a la carpeta del mes N-2
folder = Path(r'C:\Users\<USER>\Desktop\Vario CGE\_Facturacion\202411\09. IVT-Def')
año_mes = '2410'

cmg = glob.glob(str(folder / '03-Base*.zip'))[0]
# Primero encuentra el archivo de cmg y descomprime el ZIP 15 minutal, no borra el original
cmg_n_2 = get_file_from_zip(folder, cmg, f'cmg{año_mes}_15minutal.zip')

# Descomprime el archivo csv que contiene la data del zip anterior, ahora si borra el zip.
cmg_n_2 = get_file_from_zip(folder, folder / f'cmg{año_mes}_15minutal.zip', f'cmg{año_mes}_15minutal.csv', True)

# debiera quedar en lacarpeta un archivo cmg{año_mes}_15minutal.csv
# ahora se lee este archivo, se formatea, y se deja en formato parquet para usos futuros
df = lee_cmg_csv_def_to_parquet(folder / f'cmg{año_mes}_15minutal.csv')
#%% md
# Paso 6

Ahora se descargan los Costos Marginales definitivos y pereliminares del mes anterior.
Desde Agosto del 2024, se publican los definitivos 7 a 10 días después de que pase el día, por lo que hasta el día 22 - 25 debieran estar los costos marginales definitivos.

- la función "descarga_cmg_15 min", descarga desde la web, y ahorra el tiempod e ir descargando día a día, pero hay que ir completando de la siguiente forma:
     * folder: la carpeta donde se descargan los archivos
     * proceso: <u>"def" o "pre"</u>, los primeros días serán "def", y los últimos días seran "pre"
     * dia_inicial: el dia inicial de la descarga. ojo que cuando cambie el mes_publicación, hay que cambiar este numero
     * dia_final: el dia final de la descarga. <u>Este día NO se descarga</u>.
     * mes_publicacion: el mes en el que se publican los costos marginales. Al llegar al día 20 aproximadamente, este valor cambia al mes siguiente.

Esto es iterativo, y hay que ir en la página del coordinador que días y meses son los que hay que ir poniendo.
De todas formas se puede hcer todo a mano

#%%
folder = Path(r'C:\Users\<USER>\Desktop\Vario CGE\_Facturacion\202412\_CMg')
descarga_cmg_15min(
    folder=folder,
    proceso='def',
    dia_inicial=20,
    dia_final=32,
    mes='12',
    mes_publicacion='01',
    agno='24',
    agno_publicacion='25'
)
#%% md
# Paso 7

Una vez descargado todos los días del mes N, se descomprime el archivo de CMg, y se procesan para dejarlos sólo en 1 archivo, en formato parquet.

#%%
# get_file_from_zip(folder, cmg, f'cmg{año_mes}_15minutal.zip')
folder = Path(r'C:\Users\<USER>\Desktop\Vario CGE\_Facturacion\202412\_CMg')
archivo_final = 'CMg_24_11_def.parquet'
archivo_usd = 'USD_2411_def.xlsx'
elimina_csv = True
paso7 = descomprime_zip(folder)
if paso7:
    print('Archivos descomprimido, Procesando CMg')
    df_fin, df_usd = procesa_csv_cmg15min(folder, archivo_final, archivo_usd, elimina_csv)


#%% md
# Paso 8

Ahora se juntan los archivos de consumos, y de costos marginales para sacar el precio final de los clientes.
Los archivos de CostoMarginal y de consumo ".parquet" deben estar en la misma carpeta


#%%
agno = '24'
mes = '12'
proceso = 'def'

# en p_fact deben estar los archivos de cmg.parquet
#p_fact = Path(r'C:\Users\<USER>\Desktop\Vario CGE\_Facturacion\202412\03. Cálculo de precios')
p_fact = Path(r'C:\Users\<USER>\Desktop\Vario CGE\_Facturacion\202412\_CMg')

df = corre_main(agno, mes, proceso, p_fact)

#%% md
# Paso 9

Cálculo de SSCC por clientes, y reliquidación de CMg.
Se utilizan los valores de los cuadros de pago del coordinador del mes anterior, que contienen la data del mes N-2

En la carpeta de trabajo deben estar descargados del coordinador los siguientes archivos:
 - "01-Resultados_yymm_BD01.zip"
 - "03-Bases-de-Datos_yymm_BD01-2.zip"
 - "Balance_SSCC_yyyy_mmm_def"

El resultado importante quedaría en la carpeta "/Procesados", bajo el nombre de "_resumen.xlsx"


#%%
año = '25'    # año correspondiente a las medidas
mes = '01'    # data del mes N-2
proceso = 'def'
folder = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\General CGE Cx\Facturación PPAs\Cálculo Facturación\2025\202502\09. IVT-Def')

# ****** INFO para la reliquidación de CMg


proceso_sscc(año, mes, proceso, folder)
print('Paso 9 finalizado')

#%% md
# Paso 10

#%%
cgec.to_excel(folder / 'cgec_cen.xlsx', index=False)

#%% md
# Pasos que faltan

- falta explicar lo de los costos marginales del N-2, pra calcular la reliquidación
- cómo se rellena el archivo de Formato de Carga del Leo.
- Cómo se revisa, y cómo se manda

#%%
# falta explicar lo de los costos marginales del N-2, pra calcular la reliquidación
# cómo se rellena el archivo de Formato de Carga del Leo.
# Cómo se revisa, y cómo se manda

ar_cmg = f'cmg{agno}{mes}_{proceso}.parquet'
ar_consumo = f'{agno}-{mes}_{proceso}.parquet'


# en p_fact deben estar los archivos de cmg.parquet
p_fact = Path(r'C:\Users\<USER>\Desktop\Vario CGE\_Facturacion\202412\03. Cálculo de precios')


df = corre_main(agno, mes, proceso, p_fact)
#%%
from pathlib import Path


print(Path.cwd())
#%%

base = r'C:\Users\<USER>\Desktop\NT-Art-1-16-12_2023-v1.xlsx'
df = pd.read_excel(base, sheet_name='TD', header=3)
#%%
import pandas as pd
from pathlib import Path

file = r'C:\Users\<USER>\OneDrive - Grupo CGE\2023\Respaldo Carpetas\_Modelo\2023\07_Julio\07_v3'

ar = Path(file) / 'TOHCMg.parquet'

df = pd.read_parquet(ar)

#%%
barras = [
    'Atacama220_BP1', 'ElCobre220', 'Maitencillo220', 'CNavia220', 'DAlmagro220',
    'Quillota220', 'Polpaico220', 'Rapel220', 'Itahue154', 'Hualpen220',
    'Charrua220', 'Temuco220', 'AJahuel220', 'PAzucar220',
    'LVilos220', 'Chena220'
]
barras2 = '|'.join(barras)

b1 = df.loc[df['Barra'].isin(barras)]
#%%
for b in b1.Barra.unique():
    nombre = b + '.csv'
    print(Path(file) / nombre)
    df2 = b1.loc[b1['Barra'] == b]
    df2.to_csv(Path(file) / nombre, index=False)

#%%
df.loc[df['Barra'].str.contains('Almagro')]
#%%
path = Path(r'C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\All_Data')
ar = 'IVT_24_10.parquet'
df = pd.read_parquet(path / ar)
#%%
df.loc[df['propietario']=='CGE_C']
#%%
folder = Path(r'C:\Users\<USER>\Desktop\Vario CGE\_Facturacion\202501\09. IVT-Def')
año_mes = '2412'
df = lee_cmg_csv_def_to_parquet(folder / f'cmg{año_mes}_15minutal.csv')
#%%
