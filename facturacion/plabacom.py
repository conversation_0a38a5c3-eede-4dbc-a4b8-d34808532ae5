# python
"""
Scraper para https://plabacom.coordinador.cl/pages usando Scrapling.

Pasos:
- Ajusta SELECTORES_* según la estructura real de la página.
- Define los campos que quieres extraer en parse_item().
- Ejecuta: python plabacom.py
"""

from __future__ import annotations
import asyncio
import csv
import json
import random
import sys
import time
from dataclasses import dataclass, asdict
from typing import AsyncIterator, Iterator, List, Optional

# Requiere:
#   pip install scrapling
# Documentación:
#   https://scrapling.readthedocs.io/en/latest/

# NOTA: Evita credenciales; usa placeholders si alguna vez fueran necesarios.
# Respeta robots.txt y términos del sitio.

# ============= CONFIGURACIÓN =============

BASE_URL = "https://plabacom.coordinador.cl/pages"

# Ajusta estos selectores tras inspeccionar el HTML en el navegador.
# Ejemplos de nombres: ".card", ".list-item", "article", etc.
SELECTOR_LISTA_ITEMS = ".list-selector-a-definir"
SELECTOR_ITEM_LINK = "a"            # dentro del item
SELECTOR_ITEM_TITULO = ".title"     # dentro del item
SELECTOR_SIG_PAG = "a[rel='next'], .pagination-next a"

# Retrasos aleatorios entre peticiones para ser respetuosos
REQUEST_DELAY_RANGE = (0.8, 1.6)

# Output
OUTPUT_CSV = "plabacom_pages.csv"
OUTPUT_JSON = "plabacom_pages.json"


@dataclass
class PageItem:
    titulo: Optional[str]
    url: Optional[str]
    # Agrega aquí más campos si existen, por ejemplo:
    # resumen: Optional[str] = None
    # fecha: Optional[str] = None


# ============= UTILIDADES =============

def human_delay():
    time.sleep(random.uniform(*REQUEST_DELAY_RANGE))


def export_csv(items: List[PageItem], path: str) -> None:
    if not items:
        print("No hay items que exportar (CSV).")
        return
    with open(path, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=list(asdict(items[0]).keys()))
        writer.writeheader()
        for it in items:
            writer.writerow(asdict(it))
    print(f"CSV exportado: {path}")


def export_json(items: List[PageItem], path: str) -> None:
    with open(path, "w", encoding="utf-8") as f:
        json.dump([asdict(i) for i in items], f, ensure_ascii=False, indent=2)
    print(f"JSON exportado: {path}")


# ============= SCRAPING CON SCRAPLING =============
# Nota: La API exacta de Scrapling puede variar; sigue la guía oficial [[1],[3]].
# Aquí se muestra un enfoque típico: abrir página, seleccionar elementos,
# extraer campos y recorrer la paginación.

async def scrape_with_scrapling(start_url: str) -> List[PageItem]:
    """
    Recorre /pages y extrae items. Ajusta los selectores de arriba.
    """
    # Import local para permitir ejecutar el script aún si no está instalado
    try:
        # Algunas instalaciones exponen clases como Browser/Page; valida con la doc.
        from scrapling import Browser  # tipo frecuente en librerías de scraping de alto nivel
    except Exception as e:
        print("No se pudo importar Scrapling. Instálalo con: pip install scrapling", file=sys.stderr)
        raise

    items: List[PageItem] = []

    # Parâmetros típicos: headless, timeout, user_agent. Ajusta según docs.
    async with Browser(headless=True, timeout=30) as browser:
        url = start_url

        while url:
            print(f"Abriendo: {url}")
            try:
                page = await browser.open(url)
            except Exception as e:
                print(f"Error abriendo {url}: {e}", file=sys.stderr)
                # Con backoff sencillo
                time.sleep(2)
                continue

            # Si la página requiere esperar cargas dinámicas, utiliza esperas explícitas:
            # await page.wait_for_selector(SELECTOR_LISTA_ITEMS, timeout=10000)

            try:
                # Obtén los nodos de lista
                nodes = await page.query_all(SELECTOR_LISTA_ITEMS)
            except Exception as e:
                print(f"No se pudo seleccionar la lista con {SELECTOR_LISTA_ITEMS}: {e}", file=sys.stderr)
                nodes = []

            for node in nodes:
                item = await parse_item(node)
                items.append(item)

            # Paginación: busca el enlace "siguiente"
            try:
                next_link_node = await page.query_one(SELECTOR_SIG_PAG)
                if next_link_node:
                    href = await next_link_node.get_attribute("href")
                    url = href if href.startswith("http") else f"https://plabacom.coordinador.cl{href}"
                else:
                    url = None
            except Exception:
                url = None

            human_delay()

    return items


async def parse_item(node) -> PageItem:
    """
    Extrae campos de un item usando selectores internos.
    Ajusta los selectores según el HTML real.
    """
    titulo: Optional[str] = None
    url: Optional[str] = None

    try:
        title_node = await node.query_one(SELECTOR_ITEM_TITULO)
        if title_node:
            titulo = (await title_node.text_content()) or None
    except Exception:
        pass

    try:
        link_node = await node.query_one(SELECTOR_ITEM_LINK)
        if link_node:
            href = await link_node.get_attribute("href")
            if href:
                url = href if href.startswith("http") else f"https://plabacom.coordinador.cl{href}"
    except Exception:
        pass

    return PageItem(titulo=titulo, url=url)


# ============= PUNTO DE ENTRADA =============

def main():
    print("Iniciando scraping de /pages ...")
    items: List[PageItem] = asyncio.run(scrape_with_scrapling(BASE_URL))
    print(f"Total items: {len(items)}")
    export_csv(items, OUTPUT_CSV)
    export_json(items, OUTPUT_JSON)


if __name__ == "__main__":
    main()