import pandas as pd
import polars as pl
from pathlib import Path

#%%
folder = Path(r'C:\Users\<USER>\Desktop\Rev Facturación')
ivt = 'Informe_prorrata_1750716490000.csv'
cmg = 'cmg2505_15min_formateado_def.csv'
#%%
df = pl.read_csv(folder / ivt, separator=';', ignore_errors=True)
df2 = pl.read_csv(folder / cmg, separator=';', ignore_errors=True)
#%%
df_cge = df.filter(
    pl.col('Suministrador').str.to_uppercase().str.contains('CGE')
)
#%%
df_cge.to_pandas().to_excel(folder / 'df_cge_ivt.xlsx', index=False)

#%%
df_cge['Barra'].unique()
#%%
df2 = df2.drop(['nombre_barra', 'tension', 'INTERVALO', 'Cuarto de Hora'])
df2 = df2.rename({'nombre_barra_cmg': 'Barra'})
#%%

# Transformación de Clave_Anio_Mes y Hora_Mensual a FECHA, HORA, MINUTO
df_cge = df_cge.with_columns([
    # Extraer año y mes de Clave_Anio_Mes
    pl.col('Clave_Anio_Mes').cast(pl.String).str.slice(0, 2).cast(pl.Int32).alias('año'),
    pl.col('Clave_Anio_Mes').cast(pl.String).str.slice(2, 2).cast(pl.Int32).alias('mes'),

    # Calcular el día (cada día tiene 96 intervalos de 15 minutos)
    ((pl.col('Hora_Mensual') - 1) // 96 + 1).alias('dia'),

    # Calcular la hora dentro del día (1-24)
    (((pl.col('Hora_Mensual') - 1) % 96) // 4 + 1).alias('HORA'),

    # Calcular los minutos (0, 15, 30, 45)
    (((pl.col('Hora_Mensual') - 1) % 4) * 15).alias('MINUTO')
]).with_columns([
    # Crear la fecha en formato yyyymmdd
    (pl.lit('20') +
     pl.col('año').cast(pl.Utf8).str.zfill(2) +
     pl.col('mes').cast(pl.Utf8).str.zfill(2) +
     pl.col('dia').cast(pl.Utf8).str.zfill(2)).alias('FECHA')
]).drop(['año', 'mes', 'dia'])

#%%
df_cge = df_cge.drop(['Clave_Anio_Mes', 'Hora_Mensual', 'Prorrata'])
orden_col = ['FECHA', 'HORA', 'MINUTO', 'Suministrador', 'Barra', 'Retiro',
             'Clave', 'Tipo', 'Medida_kWh']
df_cge = df_cge.select(orden_col)
df_cge = df_cge.with_columns(pl.col('FECHA').cast(pl.Int64))
#%%
df2 = df2.with_columns(
    pl.col('FECHA').cast(pl.Int64),
    pl.col('HORA').cast(pl.Int64),
    pl.col('MINUTO').cast(pl.Int64)
)

columnas_temporales = ["texto_parte", "numero_str", "numero_formateado"]

df_cge2 = df_cge.with_columns(
    texto_parte = pl.col("Barra").str.slice(0, 14),
    numero_str = pl.col("Barra").str.extract(r"(\d{2,3})$", 1),
).with_columns(
    numero_formateado = pl.col("numero_str").map_elements(
        lambda x: f"{int(x):03}" if x is not None else None,
        return_dtype=pl.String
    ),
).with_columns(Barra = pl.col("texto_parte") + pl.col("numero_formateado"))
df_cge2 = df_cge2.drop(columnas_temporales)
#%%
df_fin = df_cge2.join(
    df2,
    on=['FECHA', 'HORA', 'MINUTO', 'Barra'],
    how='left')
#%%
df_fin.to_pandas().to_excel(folder / 'df_fin.xlsx', index=False)

#%%
folder = Path(r'C:\Users\<USER>\Desktop\Rev Facturación')
ivt = 'retiros_horario_2505.parquet'
cmg = 'cmg2505_15min_formateado_def.csv'
#%%
ivt = pl.read_parquet(folder / ivt)
df2 = pl.read_csv(folder / cmg, separator=';', ignore_errors=True)

df2 = df2.drop(['nombre_barra', 'tension', 'INTERVALO', 'Cuarto de Hora'])
df2 = df2.rename({'nombre_barra_cmg': 'Barra'})
#%%
cge = ivt.filter(
    pl.col('Suministrador').str.to_uppercase().str.contains('CGE')
)
#%%
cmg = df2.group_by(['FECHA', 'HORA', 'Barra']).agg([
    pl.col("CMg[USD/MWh]").mean(),
    pl.col("USD").mean()
])
#%%
columnas_temporales = ["texto_parte", "numero_str", "numero_formateado"]

df_cge = cge.with_columns(
    texto_parte = pl.col("Barra").str.slice(0, 14),
    numero_str = pl.col("Barra").str.extract(r"(\d{2,3})$", 1),
).with_columns(
    numero_formateado = pl.col("numero_str").map_elements(
        lambda x: f"{int(x):03}" if x is not None else None,
        return_dtype=pl.String
    ),
).with_columns(Barra = pl.col("texto_parte") + pl.col("numero_formateado"))
df_cge = df_cge.drop(columnas_temporales)
#%%
df_cge2 = df_cge.with_columns(
    HORA = ((pl.col("Hora Mensual") - 1) % 24 + 1),
    Dia = ((pl.col("Hora Mensual") - 1) // 24 + 1)
)
#%%
año_mes_fijo = "202505"
df_cge2 = df_cge2.with_columns(
    pl.col("Dia").cast(pl.String).str.zfill(2).alias("Dia_str"))

df_cge2 = df_cge2.with_columns(
    (pl.lit(año_mes_fijo) + pl.col("Dia_str")).alias("FECHA")
)
df_cge2 = df_cge2.with_columns(pl.col('FECHA').cast(pl.Int64))
#%%
df_fin = df_cge2.join(
    cmg,
    on=['FECHA', 'HORA', 'Barra'],
    how='left')
#%%
df_fin.to_pandas().to_excel(folder / 'df_fin2.xlsx', index=False)

#%% *****************************
folder = Path(r'C:\Users\<USER>\Desktop\Rev Facturación')
ivt = 'ConsumosTotales.xlsx'
cmg = 'cmg2505_15min_formateado_def.csv'

ivt = pd.read_excel(folder / ivt)
df2 = pl.read_csv(folder / cmg, separator=';', ignore_errors=True)

df2 = df2.drop(['nombre_barra', 'tension', 'INTERVALO', 'Cuarto de Hora'])
df2 = df2.rename({'nombre_barra_cmg': 'Barra'})
#%%

ivt['FECHA'] = '202505' + ivt['fecha_hora'].str.slice(0, 2)
ivt['FECHA'] = ivt['FECHA'].astype(int)
ivt['HORA'] = ivt['fecha_hora'].str.slice(11, 13).astype(int) + 1
ivt['MINUTO'] = ivt['fecha_hora'].str.slice(14, 17).astype(int)
#%%
ivt.rename({'BARRA': 'Barra'}, axis=1, inplace=True)
df_fin = ivt.merge(
    df2.to_pandas(),
    on=['FECHA', 'HORA', 'MINUTO', 'Barra'],
    how='left')
#%%
df_fin.to_excel(folder / 'df_fin3.xlsx', index=False)

#%%
folder = Path(r'C:\Users\<USER>\Desktop\Rev Facturación')
ivt = 'ConsumosTotales.xlsx'
cmg = 'CMg_25_05_pre.parquet'

ivt = pd.read_excel(folder / ivt)
df2 = pd.read_parquet(folder / cmg)
ivt.rename({'BARRA': 'Barra', 'fecha_hora': 'Fecha'}, axis=1, inplace=True)
#%%
ivt['Fecha'] = pd.to_datetime(ivt['Fecha'], format='%d-%m-%Y %H:%M',
                               errors='coerce')
#%%
df_fin = ivt.merge(
    df2,
    on=['Fecha', 'Barra'],
    how='left')

#%%
print(ivt.dtypes)
print(df_fin.dtypes)
#%%
df_fin.to_excel(folder / 'df_fin4.xlsx', index=False)
