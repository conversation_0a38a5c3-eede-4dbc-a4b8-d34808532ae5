from pathlib import Path
from datetime import datetime
import xlsxwriter
import polars as pl

#%%
"""
En la carpeta de trabajo deben estar descargados del coordinador 
los siguientes archivos del mes N-2:

"Balance, Sobrecostos, P.Estabilizado, Pa<PERSON>_retiros_sscc, medidas_valorizadas"

a la fecha, los bess han sido cero

El resultado importante quedaría en la carpeta "/Procesados", 
bajo el nombre de "_resumen.xlsx"
"""
carpeta = Path(r'C:\Users\<USER>\Desktop\Rev Facturación\07_Julio')

ar_balance = 'Balance_2507D.xlsx'
ar_sobrecostos = 'Pago_Sobrecostos_2507_Definitivo.xlsx'
ar_precio_estabilizado = 'Reconstruye_valorizado_pnpc_pe_2507D.xlsx'
ar_sscc_1 = '4_REMUNERACIÓN_SC_CO_CCA_Y_Pagos_Retiros_2507_def.xlsx'
ar_sscc_2 = '9_Pagos_Retiros_CRA_REA_CO_ERNC_Subastas_2507_def.xlsx'
ar_valorizado = 'Retiros_Informe_prorrata_1755903941000.csv'

archivos = {
    'Energia': [ar_balance, ar_sobrecostos],
    'Antecedentes': [ar_precio_estabilizado],
    'Balance_SSCC': [ar_sscc_1, ar_sscc_2, "Bess", ar_balance],
    '03-Bases-de-Datos': [ar_valorizado]
}

pth_out= carpeta / 'Procesados'
pth_out.mkdir(exist_ok=True)

#%% funciones
def procesa_bene(path_file, cout, sh_name='Balance Valorizado'):
    df = pl.read_excel(path_file, sheet_name=sh_name)
    cgec = df.filter((pl.col('nombre_corto_empresa') == 'CGE_C') & (
        pl.col('tipo_medidor').is_in(['L_D', 'L'])))
    cgec = cgec.drop(
        ['numero_linea', 'calificacion_linea', 'nombre_empresa', 'RUT', 'Zona'])
    #cgec.write_excel(cout / '_benergia.xlsx', autofit=True)
    write_xlsx_values(cgec, cout / "_benergia.xlsx")
    print('Proceso Balance Energía terminado')
    return cgec

def procesa_m_tec(path_file, cout, sh_name='PAGO_RETIRO'):
    df = pl.read_excel(path_file, sheet_name=sh_name)
    cgec = df.filter(pl.col('Suministrador') == 'CGE_C')
    #cgec.write_excel(cout / '_min_tec.xlsx', autofit=True)
    write_xlsx_values(cgec, cout / "_min_tec.xlsx")
    print('Proceso MTec terminado')
    return cgec

def procesa_sscc(path, files, cout):
    """
    path_files : [4_REMUNERACIÓN, 9_Pagos, BESS, Balance_]
    index:              0            1      2       3
    """
    df_salida = {}
    for i, ar in enumerate(files):
        p_arch = path / ar

        if i == 2:
            print('\tArchivo Bess no revisado')
            continue
        if i == 3:
            print('\tLeyendo archivo Balance')
            sh_name = 'Balance Comercial'
            col1 = 'Empresa'
            col2 = 'Concepto'
            filtro = 'Sobrecosto PD'
            df = pl.read_excel(p_arch, sheet_name=sh_name)
            cgec = df.filter(
                (pl.col(col1) == 'CGE_C') & (pl.col(col2) == filtro))
            cgec = cgec.with_columns(pl.col('Cantidad').abs().alias('Cantidad'))
        else:
            print(f'\tLeyendo archivo SSCC: {ar}')
            sh_name = 'PAGO_RETIRO'
            col = 'Suministrador'
            df = pl.read_excel(p_arch, sheet_name=sh_name)
            cgec = df.filter(pl.col(col) == 'CGE_C')

        df_salida[i] = cgec
        #cgec.write_excel(cout / f'_sscc_{i+1}.xlsx', autofit=True)
        write_xlsx_values(cgec, cout / f"_sscc_{i+1}.xlsx")

    print('Proceso SSCC terminado')
    return df_salida

def procesa_p_estabilizado(path_file, cout, sh_name='Compensación'):
    df = pl.read_excel(path_file, sheet_name=sh_name)
    df_fin = df.filter(pl.col('Suministrador') == 'CGE_C')
    df_fin.write_excel(cout / '_precio_est.xlsx', autofit=True)
    #write_xlsx_values(df_fin, cout / "_precio_est.xlsx")
    print('Proceso P_Estabilizado terminado')

    return df_fin

def lee_medidas_qmin(path_file, cout):
    df = pl.read_csv(path_file, ignore_errors=True, separator=';')
    df = df.drop(['nombre_barra', 'tension', 'Prorrata'])
    df = df.filter(df["Tipo"].is_in(["L", "L_D"]))
    df.write_parquet(cout / 'Medidas_15min.parquet')
    df = df.filter(pl.col("Suministrador").str.contains("CGE_C"))

    # ***************************************** pasa a horas
    agnomes = str(df.select(pl.first("Clave_Anio_Mes")).item())
    f_inicial = datetime(int("20" + agnomes[:2]), int(agnomes[-2:]), 1)

    df = df.with_columns([
        (pl.lit(f_inicial) + (pl.col("Cuarto de Hora").cast(pl.Int64) - 1) *
         pl.duration(minutes=15)).alias("cuarto_dt"), ])
    df = df.with_columns([
        pl.col("cuarto_dt").dt.date().alias("fecha"),
        pl.col("cuarto_dt").dt.hour().alias("hora"),
        pl.col("cuarto_dt").dt.minute().alias("minuto"),
    ]).with_columns(
        [pl.col("cuarto_dt").alias("Cuarto de Hora")]).drop("cuarto_dt")

    # *****************************************

    df2 = df.pivot(
        index=['fecha', 'hora', 'minuto'],
        on='clave',
        values='Medida_kWh')

    if 'GLINARES' in df2.columns:
        df2 = df2.drop(['GLINARES'], axis=1)

    df2 = df2.fill_nan(0)
    df2 = df2.with_columns(
        pl.sum_horizontal(pl.all().exclude(df2.columns[:3])).alias("Total"))
    df2 = df2.with_columns(
        [(pl.col(c) / pl.col("Total")).alias(c) for c in df2.columns[3:-1]])
    df2.write_excel(cout / f'_Medidas.xlsx', autofit=True)
    #write_xlsx_values(df2, cout / '_Medidas.xlsx')
    print('Proceso Medidas terminado')

    return df2

def compila_mt(df_e, df_mt, df_sct, cout):
    df_e = df_e.with_columns(
        [(pl.col('fisico_kwh') / pl.col('fisico_kwh').sum()).alias('pr_e'), ])
    df_e = df_e.rename({'clave_medidor': 'clave'})

    mtpc = df_mt.group_by(
        ['clave', 'Retiro', 'Barra', 'Tipo', 'Suministrador']).agg(
        pl.col(["Suma de Pago"]).sum())
    mtpc = mtpc.join(df_e.select(['clave', 'pr_e']), on='clave', how='left')

    # SSCC
    SC_CO_CCA = df_sct[0].group_by(['clave']).agg(
        pl.col(['Suma de Pago']).sum()).rename({'Suma de Pago': 'SSCC'})
    CRA_REA_CO = df_sct[1].group_by(['clave']).agg(
        pl.col(['Suma de Pago']).sum()).rename({'Suma de Pago': 'SSCC'})
    SC_CO_CCA = SC_CO_CCA.with_columns(pl.col('SSCC') + CRA_REA_CO['SSCC'])

    mtpc = mtpc.join(SC_CO_CCA, on='clave', how='left')

    # No se ha implementado el BESS

    # Mínimo Técnico, prorrata
    f_scpd = float(df_sct[3].select(pl.first("Cantidad")).item())
    mtpc = mtpc.with_columns(
        (pl.col('Suma de Pago') + pl.col('pr_e') * f_scpd).alias('SSCC_MT'))

    #mtpc.write_excel(cout / '1_MT_PC.xlsx', autofit=True)
    write_xlsx_values(mtpc, cout / '1_MT_PC.xlsx')
    return mtpc

def calc_pest_hor(df_pest, df_med, cout):
    df = df_med.hstack(df_pest.select("Compensacion [$]"))
    df = df.with_columns(
        (pl.col(c) * pl.col("Compensacion [$]")).alias(c) for c in df.columns[3:-1])
    sums = df.select(pl.col(c).alias(c) for c in df.columns[3:-2]).sum()

    dff = sums.transpose(
        include_header=True,
        header_name="clave",
        column_names=["Pr_Est"]
    )
    #dff.write_excel(cout / "2_pest_hor.xlsx", autofit=True)
    write_xlsx_values(dff, cout / "2_pest_hor.xlsx")
    return dff

def tabla_resumen(df_mt, df_horario, cout):
    df_resumen = df_mt.join(df_horario, on="clave", how="left")
    df_resumen = df_resumen.drop(['Tipo', 'Suministrador', 'Suma de Pago'])
    df_resumen = df_resumen.with_columns(pl.col("Pr_Est").abs().alias("Pr_Est"))

    #df_resumen.write_excel(cout / "_resumen.xlsx", autofit=True)
    write_xlsx_values(df_resumen, cout / "_resumen.xlsx")
    return df_resumen

def write_xlsx_values(df, path, sheet_name="Hoja1"):
    wb = xlsxwriter.Workbook(path, {'strings_to_urls': False})
    ws = wb.add_worksheet(sheet_name)

    # Escribe encabezados
    ws.write_row(0, 0, df.columns)
    # Escribe datos desde un array NumPy (rápido)
    data = df.to_numpy()  # si df es Polars: df.to_numpy()
    for i, row in enumerate(data, start=1):
        ws.write_row(i, 0, row)

    wb.close()

#%% pasos
be = procesa_bene(carpeta / archivos['Energia'][0], pth_out)
mt = procesa_m_tec(carpeta / archivos['Energia'][1], pth_out)
sc = procesa_sscc(carpeta, archivos['Balance_SSCC'], pth_out)
pest = procesa_p_estabilizado(carpeta / archivos['Antecedentes'][0], pth_out)

print('Analizando archivo de Medidas')
med = lee_medidas_qmin(carpeta / archivos['03-Bases-de-Datos'][0], pth_out)

print('Realizando calculos finales')
sscc = compila_mt(be, mt, sc, pth_out)
pr_est_h = calc_pest_hor(pest, med, pth_out)
tfin = tabla_resumen(sscc, pr_est_h, pth_out)
print('Proceso terminado')

#%%