"""
Script de inicio rápido para Data Explorer API
"""

import subprocess
import sys
import os
from pathlib import Path
import webbrowser
import time

def check_dependencies():
    """Verifica que las dependencias estén instaladas"""
    required_packages = ['fastapi', 'uvicorn', 'polars', 'pandas', 'openpyxl']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Faltan dependencias: {', '.join(missing_packages)}")
        print("💡 Instala con: pip install -r requirements_api.txt")
        return False
    
    print("✅ Todas las dependencias están instaladas")
    return True

def check_data_folder():
    """Verifica que la carpeta de datos exista"""
    # Rutas posibles para la carpeta All_Data
    possible_paths = [
        Path(r"C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\CGE_Automatizacion\All_Data"),
        Path(r"C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\Plataforma Gestión CGECx\All_Data"),
        Path("All_Data"),
        Path("../All_Data")
    ]
    
    for path in possible_paths:
        if path.exists():
            print(f"✅ Carpeta de datos encontrada: {path}")
            
            # Contar archivos
            cmg_files = list(path.glob("CMg*.parquet"))
            ivt_files = list(path.glob("IVT*.parquet"))
            
            print(f"   📁 Archivos CMg: {len(cmg_files)}")
            print(f"   📁 Archivos IVT: {len(ivt_files)}")
            
            if cmg_files or ivt_files:
                return str(path)
            else:
                print("   ⚠️  No se encontraron archivos .parquet")
    
    print("❌ No se encontró la carpeta All_Data con archivos")
    print("💡 Asegúrate de que la ruta en app_data_explorer.py sea correcta")
    return None

def start_api(data_folder=None):
    """Inicia la API"""
    print("🚀 Iniciando Data Explorer API...")
    
    # Configurar variable de entorno si se especifica
    if data_folder:
        os.environ["ALL_DATA_FOLDER"] = data_folder
    
    try:
        # Iniciar el servidor
        cmd = [sys.executable, "-m", "uvicorn", "app_data_explorer:app", 
               "--host", "0.0.0.0", "--port", "8000", "--reload"]
        
        print("📡 Servidor iniciándose en http://localhost:8000")
        print("📖 Documentación disponible en http://localhost:8000/docs")
        print("🛑 Presiona Ctrl+C para detener el servidor")
        
        # Esperar un poco y abrir el navegador
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open("http://localhost:8000")
            except:
                pass
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Ejecutar el servidor
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n🛑 Servidor detenido")
    except Exception as e:
        print(f"❌ Error iniciando el servidor: {e}")

def show_quick_examples():
    """Muestra ejemplos rápidos de uso"""
    print("\n" + "="*60)
    print("🎯 EJEMPLOS RÁPIDOS DE USO")
    print("="*60)
    
    print("\n1️⃣ LISTAR ARCHIVOS DISPONIBLES:")
    print("   GET http://localhost:8000/files/available")
    
    print("\n2️⃣ VER COLUMNAS DE CMG:")
    print("   GET http://localhost:8000/files/columns/CMg")
    
    print("\n3️⃣ VISTA PREVIA DE DATOS:")
    print("   GET http://localhost:8000/data/preview?file_type=CMg&year=2024&month=12&limit=5")
    
    print("\n4️⃣ FILTRAR DATOS CMG POR BARRA:")
    print("""   POST http://localhost:8000/data/filter
   {
       "file_type": "CMg",
       "year_start": 2024,
       "month_start": 11,
       "year_end": 2024,
       "month_end": 12,
       "filters": {"Barra": "QUIANI"},
       "export_format": "excel"
   }""")
    
    print("\n5️⃣ FILTRAR DATOS IVT POR CLIENTE:")
    print("""   POST http://localhost:8000/data/filter
   {
       "file_type": "IVT",
       "year_start": 2024,
       "month_start": 10,
       "year_end": 2024,
       "month_end": 12,
       "filters": {"Cliente": "CASINO", "propietario": "CGE_C"},
       "export_format": "excel"
   }""")
    
    print("\n💡 USAR CON PYTHON:")
    print("   python examples_usage.py")
    
    print("\n📖 DOCUMENTACIÓN COMPLETA:")
    print("   http://localhost:8000/docs")

def main():
    """Función principal"""
    print("🔧 DATA EXPLORER API - INICIO RÁPIDO")
    print("="*50)
    
    # 1. Verificar dependencias
    if not check_dependencies():
        return
    
    print()
    
    # 2. Verificar carpeta de datos
    data_folder = check_data_folder()
    
    print()
    
    # 3. Mostrar ejemplos
    show_quick_examples()
    
    print("\n" + "="*60)
    
    # 4. Preguntar si iniciar
    response = input("¿Iniciar el servidor API? (s/n): ").lower().strip()
    
    if response in ['s', 'si', 'sí', 'y', 'yes']:
        start_api(data_folder)
    else:
        print("👋 Para iniciar manualmente: python app_data_explorer.py")

if __name__ == "__main__":
    main()
