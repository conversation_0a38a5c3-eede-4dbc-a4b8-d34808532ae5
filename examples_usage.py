"""
Ejemplos de uso de la API Data Explorer
"""

import requests
import json
from datetime import datetime

# URL base de la API (ajustar según tu configuración)
BASE_URL = "http://localhost:8000"

def test_api_connection():
    """Prueba la conexión con la API"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"✅ API conectada: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ No se puede conectar a la API. ¿Está ejecutándose?")
        return False

def get_available_files():
    """Obtiene la lista de archivos disponibles"""
    response = requests.get(f"{BASE_URL}/files/available")
    if response.status_code == 200:
        files = response.json()
        print(f"📁 Archivos disponibles: {len(files)}")
        
        # Mostrar algunos ejemplos
        for file_info in files[:5]:
            print(f"  - {file_info['filename']} ({file_info['size_mb']} MB)")
        
        return files
    else:
        print(f"❌ Error obteniendo archivos: {response.status_code}")
        return []

def get_columns_info(file_type="CMg"):
    """Obtiene información de las columnas de un tipo de archivo"""
    response = requests.get(f"{BASE_URL}/files/columns/{file_type}")
    if response.status_code == 200:
        info = response.json()
        print(f"📊 Columnas en archivos {file_type}:")
        for col in info['columns'][:10]:  # Mostrar primeras 10
            print(f"  - {col}")
        return info
    else:
        print(f"❌ Error obteniendo columnas: {response.status_code}")
        return {}

def preview_data(file_type="CMg", year=2024, month=12, limit=5):
    """Obtiene una vista previa de los datos"""
    params = {
        "file_type": file_type,
        "year": year,
        "month": month,
        "limit": limit
    }
    
    response = requests.get(f"{BASE_URL}/data/preview", params=params)
    if response.status_code == 200:
        data = response.json()
        print(f"👀 Vista previa de {data['file']}:")
        print(f"   Total filas en archivo: {data['total_rows_in_file']}")
        print(f"   Columnas: {len(data['columns'])}")
        
        # Mostrar primeras filas
        for i, row in enumerate(data['data'][:3]):
            print(f"   Fila {i+1}: {dict(list(row.items())[:3])}...")  # Primeras 3 columnas
        
        return data
    else:
        print(f"❌ Error en vista previa: {response.status_code}")
        return {}

def filter_cmg_data():
    """Ejemplo: Filtrar datos CMg por barra específica"""
    filter_request = {
        "file_type": "CMg",
        "year_start": 2024,
        "month_start": 11,
        "year_end": 2024,
        "month_end": 12,
        "filters": {
            "Barra": "QUIANI"  # Filtrar por barra que contenga "QUIANI"
        },
        "export_format": "excel",
        "output_filename": "cmg_quiani_nov_dic_2024"
    }
    
    response = requests.post(f"{BASE_URL}/data/filter", json=filter_request)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Filtrado exitoso:")
        print(f"   Filas encontradas: {result['total_rows']}")
        print(f"   Archivos procesados: {len(result['processed_files'])}")
        print(f"   URL de descarga: {BASE_URL}{result['download_url']}")
        
        return result
    else:
        print(f"❌ Error en filtrado: {response.status_code}")
        print(response.text)
        return {}

def filter_ivt_data():
    """Ejemplo: Filtrar datos IVT por cliente específico"""
    filter_request = {
        "file_type": "IVT",
        "year_start": 2024,
        "month_start": 10,
        "year_end": 2024,
        "month_end": 12,
        "filters": {
            "Cliente": "CASINO",  # Filtrar por cliente que contenga "CASINO"
            "propietario": "CGE_C"  # Y que el propietario sea CGE_C
        },
        "export_format": "excel",
        "output_filename": "ivt_casino_cge_oct_dic_2024"
    }
    
    response = requests.post(f"{BASE_URL}/data/filter", json=filter_request)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Filtrado IVT exitoso:")
        print(f"   Filas encontradas: {result['total_rows']}")
        print(f"   Archivos procesados: {len(result['processed_files'])}")
        print(f"   URL de descarga: {BASE_URL}{result['download_url']}")
        
        return result
    else:
        print(f"❌ Error en filtrado IVT: {response.status_code}")
        print(response.text)
        return {}

def download_file(download_url):
    """Descarga un archivo exportado"""
    response = requests.get(f"{BASE_URL}{download_url}")
    if response.status_code == 200:
        # Extraer nombre del archivo de la URL
        filename = download_url.split('/')[-1]
        
        with open(filename, 'wb') as f:
            f.write(response.content)
        
        print(f"📥 Archivo descargado: {filename}")
        return filename
    else:
        print(f"❌ Error descargando archivo: {response.status_code}")
        return None

def run_complete_example():
    """Ejecuta un ejemplo completo de uso de la API"""
    print("🚀 Iniciando ejemplo completo de Data Explorer API\n")
    
    # 1. Probar conexión
    if not test_api_connection():
        return
    
    print("\n" + "="*50)
    
    # 2. Obtener archivos disponibles
    files = get_available_files()
    
    print("\n" + "="*50)
    
    # 3. Obtener información de columnas
    cmg_info = get_columns_info("CMg")
    ivt_info = get_columns_info("IVT")
    
    print("\n" + "="*50)
    
    # 4. Vista previa de datos
    preview_data("CMg", 2024, 12, 3)
    
    print("\n" + "="*50)
    
    # 5. Filtrar datos CMg
    print("🔍 Filtrando datos CMg...")
    cmg_result = filter_cmg_data()
    
    print("\n" + "="*50)
    
    # 6. Filtrar datos IVT
    print("🔍 Filtrando datos IVT...")
    ivt_result = filter_ivt_data()
    
    print("\n" + "="*50)
    
    # 7. Descargar archivos (opcional)
    print("📥 Para descargar los archivos, usa:")
    if cmg_result and 'download_url' in cmg_result:
        print(f"   CMg: {BASE_URL}{cmg_result['download_url']}")
    if ivt_result and 'download_url' in ivt_result:
        print(f"   IVT: {BASE_URL}{ivt_result['download_url']}")
    
    print("\n✅ Ejemplo completo finalizado!")

def create_custom_filter():
    """Ejemplo de filtro personalizado más complejo"""
    filter_request = {
        "file_type": "IVT",
        "year_start": 2024,
        "month_start": 1,
        "year_end": 2024,
        "month_end": 12,
        "filters": {
            "propietario": "CGE_C",
            "Cliente": ["CASINO", "AITUE", "KUDEN"]  # Lista de clientes específicos
        },
        "export_format": "excel",
        "output_filename": "clientes_especificos_2024"
    }
    
    response = requests.post(f"{BASE_URL}/data/filter", json=filter_request)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Filtro personalizado exitoso:")
        print(f"   Filas: {result['total_rows']}")
        print(f"   Rango: {result['date_range']}")
        return result
    else:
        print(f"❌ Error en filtro personalizado: {response.status_code}")
        return {}

if __name__ == "__main__":
    # Ejecutar ejemplo completo
    run_complete_example()
    
    print("\n" + "="*50)
    print("🎯 Ejemplo de filtro personalizado:")
    create_custom_filter()
