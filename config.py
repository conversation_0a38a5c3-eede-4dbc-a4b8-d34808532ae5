"""
Configuración para la aplicación Data Explorer
"""

from pathlib import Path
import os

# Configuración de rutas
ALL_DATA_FOLDER = Path(os.getenv("ALL_DATA_FOLDER", r"C:\Users\<USER>\OneDrive - Grupo CGE\BBDD\CGE_Automatizacion\All_Data"))
TEMP_FOLDER = Path(os.getenv("TEMP_FOLDER", "temp_exports"))

# Configuración de la aplicación
APP_CONFIG = {
    "title": "Data Explorer API",
    "description": "API para explorar y filtrar datos de archivos CMg e IVT",
    "version": "1.0.0",
    "host": "0.0.0.0",
    "port": 8000
}

# Configuración de archivos
FILE_PATTERNS = {
    "CMg": "CMg_{year}_{month}_def.parquet",
    "IVT": "IVT_{year}_{month}.parquet"
}

# Límites de la aplicación
LIMITS = {
    "max_preview_rows": 1000,
    "max_export_rows": 1000000,  # 1 millón de filas máximo
    "temp_file_lifetime_hours": 2
}

# Formatos de exportación soportados
EXPORT_FORMATS = {
    "excel": {
        "extension": ".xlsx",
        "media_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    },
    "csv": {
        "extension": ".csv", 
        "media_type": "text/csv"
    },
    "parquet": {
        "extension": ".parquet",
        "media_type": "application/octet-stream"
    }
}

# Filtros comunes predefinidos
COMMON_FILTERS = {
    "CMg": {
        "barra_examples": ["QUIANI", "ALMAGRO", "CARDONES"],
        "typical_columns": ["Barra", "Fecha", "CMg [USD/MWh]", "USD"]
    },
    "IVT": {
        "cliente_examples": ["CGE_C", "CASINO", "AITUE"],
        "propietario_examples": ["CGE_C", "ENEL"],
        "typical_columns": ["Cliente", "propietario", "nombre_barra", "Fecha", "Consumo [kWh]"]
    }
}
