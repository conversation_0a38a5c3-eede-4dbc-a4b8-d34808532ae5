# Data Explorer API

Una aplicación FastAPI para explorar y filtrar rápidamente información de archivos CMg e IVT almacenados en formato Parquet.

## 🚀 Características

- **Exploración rápida**: Lista archivos disponibles por año y mes
- **Vista previa**: Examina datos sin descargar archivos completos
- **Filtrado avanzado**: Aplica múltiples filtros a los datos
- **Exportación flexible**: Exporta a Excel, CSV o Parquet
- **API REST**: Interfaz programática fácil de usar
- **Documentación automática**: Swagger UI y ReDoc incluidos

## 📁 Estructura de Archivos Soportada

La aplicación espera archivos en la carpeta `All_Data` con el siguiente formato:

### Archivos CMg
```
CMg_YY_MM_def.parquet
Ejemplo: CMg_24_12_def.parquet (Diciembre 2024)
```

### Archivos IVT
```
IVT_YY_MM.parquet
Ejemplo: IVT_24_12.parquet (Diciembre 2024)
```

## 🛠️ Instalación

1. **Instalar dependencias**:
```bash
pip install -r requirements_api.txt
```

2. **Configurar ruta de datos** (opcional):
Edita la variable `ALL_DATA_FOLDER` en `app_data_explorer.py` o usa variable de entorno:
```bash
export ALL_DATA_FOLDER="/ruta/a/tu/carpeta/All_Data"
```

3. **Ejecutar la aplicación**:
```bash
python app_data_explorer.py
```

O usando uvicorn directamente:
```bash
uvicorn app_data_explorer:app --host 0.0.0.0 --port 8000 --reload
```

## 🌐 Uso de la API

### Acceso Web
- **Página principal**: http://localhost:8000
- **Documentación Swagger**: http://localhost:8000/docs
- **Documentación ReDoc**: http://localhost:8000/redoc

### Endpoints Principales

#### 1. Listar Archivos Disponibles
```http
GET /files/available
GET /files/available?file_type=CMg&year=2024
```

#### 2. Obtener Columnas de un Tipo de Archivo
```http
GET /files/columns/CMg
GET /files/columns/IVT
```

#### 3. Vista Previa de Datos
```http
GET /data/preview?file_type=CMg&year=2024&month=12&limit=10
```

#### 4. Filtrar y Exportar Datos
```http
POST /data/filter
Content-Type: application/json

{
    "file_type": "CMg",
    "year_start": 2024,
    "month_start": 11,
    "year_end": 2024,
    "month_end": 12,
    "filters": {
        "Barra": "QUIANI"
    },
    "export_format": "excel",
    "output_filename": "cmg_quiani_nov_dic"
}
```

#### 5. Descargar Archivo Exportado
```http
GET /download/{filename}
```

## 📊 Ejemplos de Filtros

### Filtrar CMg por Barra
```json
{
    "file_type": "CMg",
    "year_start": 2024,
    "month_start": 1,
    "year_end": 2024,
    "month_end": 12,
    "filters": {
        "Barra": "QUIANI"
    },
    "export_format": "excel"
}
```

### Filtrar IVT por Cliente y Propietario
```json
{
    "file_type": "IVT",
    "year_start": 2024,
    "month_start": 6,
    "year_end": 2024,
    "month_end": 12,
    "filters": {
        "Cliente": "CASINO",
        "propietario": "CGE_C"
    },
    "export_format": "excel"
}
```

### Filtrar por Lista de Valores
```json
{
    "file_type": "IVT",
    "year_start": 2024,
    "month_start": 1,
    "year_end": 2024,
    "month_end": 12,
    "filters": {
        "Cliente": ["CASINO", "AITUE", "KUDEN"],
        "propietario": "CGE_C"
    },
    "export_format": "excel"
}
```

## 🐍 Uso Programático

Ejecuta el archivo de ejemplos:
```bash
python examples_usage.py
```

O usa requests directamente:
```python
import requests

# Obtener archivos disponibles
response = requests.get("http://localhost:8000/files/available")
files = response.json()

# Filtrar datos
filter_request = {
    "file_type": "CMg",
    "year_start": 2024,
    "month_start": 11,
    "year_end": 2024,
    "month_end": 12,
    "filters": {"Barra": "QUIANI"},
    "export_format": "excel"
}

response = requests.post("http://localhost:8000/data/filter", json=filter_request)
result = response.json()

# Descargar archivo
download_url = result['download_url']
file_response = requests.get(f"http://localhost:8000{download_url}")
with open("resultado.xlsx", "wb") as f:
    f.write(file_response.content)
```

## ⚙️ Configuración

### Variables de Entorno
- `ALL_DATA_FOLDER`: Ruta a la carpeta con archivos Parquet
- `TEMP_FOLDER`: Carpeta para archivos temporales de exportación

### Límites por Defecto
- Vista previa: máximo 1,000 filas
- Exportación: máximo 1,000,000 filas
- Archivos temporales: se eliminan después de 2 horas

## 🔧 Personalización

Para adaptar la aplicación a tu estructura de archivos específica:

1. **Modificar patrones de archivos** en `config.py`
2. **Ajustar filtros comunes** para tus datos
3. **Cambiar límites** según tus necesidades

## 📝 Notas Importantes

- Los archivos temporales se eliminan automáticamente
- Los filtros de texto usan búsqueda que "contiene" (case-insensitive)
- Los filtros de lista usan búsqueda exacta
- La aplicación maneja automáticamente archivos faltantes

## 🐛 Solución de Problemas

### Error: "Archivo no encontrado"
- Verifica que la ruta `ALL_DATA_FOLDER` sea correcta
- Confirma que los archivos sigan el patrón de nombres esperado

### Error: "No se puede conectar a la API"
- Verifica que la aplicación esté ejecutándose
- Confirma que el puerto 8000 esté disponible

### Rendimiento lento
- Reduce el rango de fechas
- Aplica filtros más específicos
- Considera usar formato Parquet para exportación

## 📞 Soporte

Para problemas o mejoras, revisa:
1. Los logs de la aplicación
2. La documentación en `/docs`
3. Los ejemplos en `examples_usage.py`
